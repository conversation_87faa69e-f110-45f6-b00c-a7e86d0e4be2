/* Apple-inspired design for side panel */
:root {
  --primary-color: #0071e3;
  --secondary-color: #86868b;
  --background-color: #f5f5f7;
  --card-background: #ffffff;
  --text-color: #1d1d1f;
  --border-radius: 12px;
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
}

.container {
  width: 100%;
  max-width: 100%;
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

header {
  text-align: center;
  margin-bottom: 16px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

h1 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}

h2 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.date-time-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-inputs {
  display: flex;
  gap: 8px;
}

.input-group {
  width: 100%;
}

.time-input-group {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: var(--secondary-color);
}

input[type="date"],
input[type="number"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

input[type="date"]:focus,
input[type="number"]:focus {
  border-color: var(--primary-color);
}

/* Styles pour les champs auto-mis à jour */
.auto-updated-field {
  background-color: #f0f8ff; /* Légère teinte bleue pour indiquer l'auto-mise à jour */
  border-color: #b8d4f5;
  cursor: not-allowed;
}

.auto-update-label {
  font-size: 10px;
  color: var(--primary-color);
  font-style: italic;
  margin-left: 4px;
}

/* Styles spécifiques pour les champs numériques */
input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
  appearance: textfield; /* Standard */
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

button:hover {
  background-color: #0062cc;
}

/* Section pour la Montre astro suisse */
.swiss-astro-watch-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.swiss-astro-watch-section h2 {
  text-align: center;
}

.watch-description {
  text-align: center;
  margin-bottom: 12px;
  color: var(--secondary-color);
  font-size: 14px;
}

.watch-time {
  text-align: center;
  margin: 8px 0;
}

.time-display {
  font-family: monospace;
  font-weight: bold;
  font-size: 16px;
  color: #333;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ccc;
  transition: background-color 0.2s, color 0.2s;
}

.time-updated {
  background-color: #0071e3;
  color: white;
  border-color: #0062cc;
}

/* Disposition horizontale pour la montre */
.watch-layout {
  display: flex;
  gap: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  align-items: flex-start;
}

.main-watch-container {
  flex: 2;
  min-width: 500px;
}

#swiss-watch-canvas {
  width: 100%;
  height: 500px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.side-circles-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 300px;
}

.side-circle-section {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
}

.side-circle-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
  text-align: center;
}

#transit-360-canvas,
#transit-30-canvas {
  width: 100%;
  height: 240px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: white;
}

/* Responsivité pour les petits écrans */
@media (max-width: 768px) {
  .watch-layout {
    flex-direction: column;
    gap: 15px;
  }

  .main-watch-container {
    min-width: auto;
  }

  .side-circles-container {
    min-width: auto;
    flex-direction: row;
    gap: 15px;
  }

  .side-circle-section {
    flex: 1;
  }

  #transit-360-canvas,
  #transit-30-canvas {
    height: 200px;
  }
}

/* Légende de la montre astro suisse */
.swiss-watch-legend {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.legend-section {
  margin-bottom: 12px;
}

.legend-section:last-child {
  margin-bottom: 0;
}

.legend-section h4 {
  font-size: 13px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 6px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 4px;
}

.legend-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-line {
  width: 20px;
  height: 3px;
  border-radius: 2px;
  display: inline-block;
}

.moon-line {
  background-color: #888;
}

.asc-t-line {
  background-color: #FF9900;
}

.desc-t-line {
  background-color: #00CC66;
}

.legend-text {
  font-size: 12px;
  color: #333;
}

/* Section dédiée à la carte du ciel */
.chart-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.chart-section h2 {
  text-align: center;
}

.chart-subtitle {
  display: block;
  font-size: 12px;
  font-weight: normal;
  color: var(--secondary-color);
  margin-top: 4px;
}

.chart-wrapper {
  width: 100%;
  aspect-ratio: 1 / 1;
  height: 600px; /* Augmenté de 400px à 600px */
  min-height: 500px; /* Hauteur minimale */
  max-height: 80vh; /* Hauteur maximale adaptative */
  position: relative;
  margin: 0 auto;
}

#chart-canvas {
  width: 100%;
  height: 100%;
}

/* Responsivité pour la section chart */
@media (max-width: 768px) {
  .chart-wrapper {
    height: 500px;
    min-height: 400px;
    max-height: 70vh;
  }
}

@media (max-width: 480px) {
  .chart-wrapper {
    height: 400px;
    min-height: 350px;
    max-height: 60vh;
  }
}

/* Section pour les tableaux de positions côte à côte */
.positions-section {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.positions-container {
  flex: 1;
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  min-width: 0; /* Pour éviter le débordement */
}

.positions-list {
  font-size: 13px;
  max-height: 200px;
  overflow-y: auto;
}

.planet-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.planet-name {
  font-weight: 500;
}

.planet-degree {
  color: var(--secondary-color);
}

/* Section pour les aspects */
.aspects-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.aspects-filter {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f7;
  border-radius: 8px;
}

.filter-group {
  margin-bottom: 12px;
}

.filter-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  font-size: 14px;
}

.orb-filter-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.orb-filter-controls input[type="range"] {
  flex: 1;
}

#aspect-orb-value {
  font-weight: 500;
  color: var(--primary-color);
  min-width: 30px;
  text-align: center;
}

.aspect-types-filter {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 8px;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  cursor: pointer;
}

.filter-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
}

.filter-btn:hover {
  background-color: #0062cc;
}

.aspects-list {
  font-size: 13px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
}

.aspect-item {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.aspect-item > div:first-child {
  flex: 1;
  min-width: 150px;
  margin-right: 10px;
}

.aspect-item > div:nth-child(2) {
  width: 80px;
  text-align: right;
  margin-right: 10px;
}

/* Section pour les maisons astrologiques */
.houses-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.houses-section h2 {
  text-align: center;
  margin-bottom: 16px;
}

.location-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.location-input {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.coordinates {
  display: flex;
  gap: 12px;
}

.coordinate-input {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.location-settings input {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
}

.houses-options {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

#house-system-select {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  background-color: #fff;
  width: 100%;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
}

.radio-label input[type="radio"],
.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.checkbox-group {
  display: flex;
  gap: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
}

.date-display {
  text-align: center;
  margin: 12px 0;
  padding: 8px;
  background-color: #f5f5f7;
  border-radius: 6px;
  font-size: 14px;
}

#houses-date-display {
  font-weight: 600;
  color: var(--primary-color);
}

.houses-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.houses-chart {
  width: 100%;
  height: 300px;
  position: relative;
}

#houses-canvas {
  width: 100%;
  height: 100%;
}

.houses-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
}

.house-item {
  background-color: #f5f5f7;
  border-radius: 6px;
  padding: 10px;
  font-size: 13px;
}

.house-number {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--primary-color);
}

.house-sign {
  display: flex;
  justify-content: space-between;
}

.house-cusp {
  color: var(--secondary-color);
}

.house-mode, .house-system, .house-inverted {
  font-size: 11px;
  color: #666;
  font-style: italic;
  margin-top: 4px;
}

.house-inverted {
  color: #e74c3c;
}

/* Section pour les zodiaques verticaux */
.vertical-zodiacs-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.vertical-zodiacs-section h2 {
  text-align: center;
  margin-bottom: 16px;
}

.vertical-zodiacs-section h3 {
  font-size: 14px;
  text-align: center;
  margin-bottom: 10px;
}

.zodiacs-container {
  display: flex;
  gap: 16px;
  justify-content: space-between;
}

.zodiac-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.vertical-zodiac {
  width: 100%;
  height: 360px;
  position: relative;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.vertical-zodiac canvas {
  width: 100%;
  height: 100%;
}

/* Styles pour les sections d'aspects des zodiacs */
.zodiac-aspects-section {
  width: 100%;
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f7;
  border-radius: 8px;
}

.zodiac-aspects-section h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: var(--primary-color);
  text-align: center;
}

.zodiac-aspects-list {
  max-height: 200px;
  overflow-y: auto;
  font-size: 13px;
}

.zodiac-aspect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee;
  margin-bottom: 6px;
}

.zodiac-aspect-info {
  flex: 1;
}

.zodiac-aspect-planets {
  font-weight: 500;
}

.zodiac-aspect-details {
  color: var(--secondary-color);
  font-size: 12px;
  margin-top: 2px;
}

.zodiac-aspect-interpret-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
}

.zodiac-aspect-interpret-btn:hover {
  background-color: #0062cc;
}

.more-aspects-message {
  text-align: center;
  font-style: italic;
  color: var(--secondary-color);
  font-size: 12px;
  margin-top: 8px;
  padding: 4px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.empty-aspects-message {
  text-align: center;
  font-style: italic;
  color: var(--secondary-color);
  padding: 10px;
}

/* Styles pour les filtres des aspects des zodiacs */
.zodiac-aspects-filter {
  margin-bottom: 12px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.filter-row {
  margin-bottom: 8px;
}

.filter-row label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-color);
}

.orb-slider {
  width: 60%;
  margin-right: 8px;
}

.orb-value {
  font-weight: 500;
  color: var(--primary-color);
  font-size: 12px;
  min-width: 25px;
  display: inline-block;
}

.aspect-types-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.aspect-checkbox {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  cursor: pointer;
  background-color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ddd;
  transition: background-color 0.2s;
}

.aspect-checkbox:hover {
  background-color: #f5f5f5;
}

.aspect-checkbox input[type="checkbox"] {
  margin: 0;
  width: 12px;
  height: 12px;
}

.apply-filter-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 6px;
  width: 100%;
}

.apply-filter-btn:hover {
  background-color: #0062cc;
}

/* Section pour le zodiaque circulaire proportionnel */
.circular-zodiac-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.circular-zodiac-section h2 {
  text-align: center;
  margin-bottom: 8px;
  color: #333;
}

.circular-zodiac-description {
  text-align: center;
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

/* Zone d'affichage des commentaires du jour */
.day-comments-display {
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.day-comments-display:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.comment-title {
  font-weight: bold;
  font-size: 16px;
  color: black;
  text-align: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ddd;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.comment-date {
  font-weight: bold;
  color: black;
  font-size: 14px;
}

.comment-indicator {
  font-size: 16px;
  opacity: 0.8;
}

.comment-text {
  color: black;
  font-size: 13px;
  line-height: 1.4;
  font-weight: 500;
  min-height: 18px;
  word-wrap: break-word;
}

.comment-text.no-comment {
  opacity: 0.6;
  font-style: italic;
  font-weight: normal;
}

.circular-zodiac-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 500px;
  position: relative;
  overflow: hidden;
}

.circular-zodiac-container canvas {
  width: 100%;
  height: 100%;
  max-width: 500px;
  max-height: 500px;
}

/* Légende du zodiaque circulaire proportionnel */
.circular-zodiac-legend {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.legend-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.legend-row:last-child {
  margin-bottom: 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #333;
}

.legend-line {
  width: 20px;
  height: 2px;
  display: inline-block;
}

/* Styles pour la section des aspects 360° dans la légende */
.legend-section {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.legend-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  margin-top: 0;
}

.legend-note {
  margin-top: 8px;
  text-align: center;
}

.legend-note small {
  font-size: 11px;
}

/* Styles pour le tooltip des aspects 360° */
#aspect-tooltip-360 {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10000;
  pointer-events: none;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  max-width: 300px;
}

#aspect-tooltip-360 div {
  margin: 2px 0;
}

#aspect-tooltip-360 div:first-child {
  font-weight: bold;
  margin-bottom: 4px;
}

.legend-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
  background-color: #ffffff;
  border: 2px solid;
}

/* Couleurs des lignes d'angles */
.asc-line {
  background-color: #ff0000;
}

.desc-line {
  background-color: #00aa00;
}

.mc-line {
  background-color: #4a90e2;
}

.ic-line {
  background-color: #8e44ad;
}

.asc-t-line {
  background-color: #ffa500;
}

.desc-t-line {
  background-color: #00cccc;
}

/* Couleurs des cercles de planètes */
.natal-circle {
  border-color: #ff0000;
}

.transit-circle {
  border-color: #4a90e2;
}

.legend-text {
  font-weight: 500;
}

/* Section pour les prédictions d'aspects */
.future-aspects-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.future-aspects-section h2 {
  text-align: center;
  margin-bottom: 16px;
}

.aspect-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.aspect-type-selector,
.prediction-start-date,
.prediction-period,
.transit-planets-selector,
.natal-planets-selector {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.planets-selector-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 12px;
}

.aspect-type-selector select,
.prediction-start-date input,
.prediction-period input {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
}

.aspect-selection-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

#selected-aspects-summary {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  min-height: 20px;
}

.manage-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.manage-btn:hover {
  background-color: var(--primary-color-dark);
}

/* Styles pour la fenêtre modale des aspects futurs */
.future-aspects-content {
  max-width: 600px;
  width: 90%;
}

.future-aspects-description {
  margin-bottom: 16px;
}

.aspect-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 20px;
}

.aspect-checkbox-label {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.aspect-checkbox-label:hover {
  background-color: #e8e8e8;
}

.aspect-checkbox-label input[type="checkbox"] {
  margin-right: 8px;
}

.standard-aspects-section,
.custom-aspects-section {
  margin-bottom: 24px;
}

.standard-aspects-section h3,
.custom-aspects-section h3 {
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--primary-color);
}

.custom-aspect-inputs {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.input-group input {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
}

#add-custom-future-aspect-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

#add-custom-future-aspect-btn:hover {
  background-color: var(--primary-color-dark);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-btn:hover {
  background-color: var(--primary-color-dark);
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.secondary-btn:hover {
  background-color: #e8e8e8;
}

/* Styles pour les aspects personnalisés dans la liste */
#custom-future-aspects-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.custom-aspect-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 6px;
}

.custom-aspect-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-aspect-actions {
  display: flex;
  gap: 8px;
}

.delete-aspect-btn {
  background-color: #ff4d4d;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.delete-aspect-btn:hover {
  background-color: #e60000;
}

.planet-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.planet-checkbox-label {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.planet-checkbox-label:hover {
  background-color: #e8e8e8;
}

.planet-checkbox-label input {
  margin-right: 5px;
}

#custom-aspect-container {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 8px;
  display: none;
}

#custom-aspect-container label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

#custom-aspect-angle {
  width: 100%;
  padding: 6px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: var(--font-family);
  font-size: 14px;
}

.future-aspects-results {
  max-height: 300px;
  overflow-y: auto;
}

/* Styles pour la fenêtre modale des aspects futurs */
.future-aspects-content {
  max-width: 600px;
}

.future-aspects-description {
  margin-bottom: 20px;
}

.future-aspects-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.empty-aspects-message {
  font-style: italic;
  color: #888;
  text-align: center;
  padding: 10px;
}

/* Section IA */
.ai-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.ai-section h2 {
  text-align: center;
  margin-bottom: 16px;
  color: #333;
}

.ai-description {
  margin-bottom: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.ai-interface {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.prompt-container,
.response-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.prompt-container label,
.response-container label {
  font-weight: bold;
  color: #333;
}

#ai-prompt {
  min-height: 80px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
  resize: vertical;
}

.ai-model-selector {
  margin-top: 8px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-model-selector label {
  font-weight: bold;
  color: #333;
  min-width: 60px;
}

.ai-model-selector select {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
  background-color: #f5f5f5;
}

.ai-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

#send-prompt-btn {
  padding: 8px 16px;
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

#send-prompt-btn:hover {
  background-color: #0058b0;
}

#send-prompt-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.ai-status {
  font-size: 14px;
  color: #666;
}

.ai-response {
  min-height: 150px;
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
}

.ai-placeholder {
  color: #999;
  font-style: italic;
}

.ai-response .error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.ai-response .suggestion {
  color: #1976d2;
  background-color: #e3f2fd;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  font-style: italic;
}

.ai-thinking {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 20px;
}

.ai-thinking:after {
  content: "...";
  position: absolute;
  width: 100%;
  text-align: left;
  animation: thinking 1.5s infinite;
}

@keyframes thinking {
  0% { content: "."; }
  33% { content: ".."; }
  66% { content: "..."; }
}

#future-aspects-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

#future-aspects-table th,
#future-aspects-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

#future-aspects-table th {
  font-weight: 600;
  color: var(--text-color);
  background-color: #f9f9f9;
  position: sticky;
  top: 0;
}

#future-aspects-table tr:hover {
  background-color: #f5f5f7;
}

.aspect-date {
  font-weight: 500;
  color: var(--primary-color);
}

.future-aspect-interpret-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.future-aspect-interpret-btn:hover {
  background-color: #1976d2;
}

/* Styles pour les informations du système de calcul */
.chart-system-info {
  background-color: #f0f8ff;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  border-left: 3px solid var(--primary-color);
  font-size: 12px;
}

.system-label {
  font-weight: 600;
  color: #333;
}

.system-name {
  color: var(--primary-color);
  font-weight: 500;
}

/* Styles pour les noms de planètes */
.planet-french-name {
  font-weight: 600;
  color: #333;
}

.planet-english-name {
  font-size: 11px;
  color: #666;
  font-style: italic;
  margin-left: 4px;
}

/* Styles pour les aspects proportionnels */
.proportional-aspects-info {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 12px;
  font-size: 12px;
}

.proportional-aspects-info p {
  margin: 0;
  color: #856404;
}

.proportional-aspects-info strong {
  color: #533f03;
}

/* Section pour le bulletin météo astrologique */
.astro-weather-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.astro-weather-section h2 {
  text-align: center;
  margin-bottom: 8px;
  color: #333;
}

.astro-weather-description {
  text-align: center;
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

.astro-weather-settings {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
}

.settings-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.settings-row:last-child {
  margin-bottom: 0;
}

.setting-group {
  flex: 1;
  min-width: 150px;
}

.setting-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #444;
  font-size: 14px;
}

.setting-group select,
.setting-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-family: var(--font-family);
  font-size: 14px;
  background-color: white;
}

.setting-group select[multiple] {
  height: 80px;
}

#calculate-weather-btn {
  width: 100%;
  padding: 10px;
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 6px;
  font-family: var(--font-family);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

#calculate-weather-btn:hover {
  background-color: #0058b0;
}

.astro-weather-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.weather-period {
  margin-bottom: 16px;
}

.period-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 8px;
}

.period-nav-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.period-nav-btn:hover {
  background-color: #0058b0;
}

#period-dates {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.export-gcal-btn {
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.3s;
}

.export-gcal-btn:hover {
  background-color: #3367D6;
}

.gcal-icon {
  font-size: 14px;
}

.weather-stats-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.weather-stat-item {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid #0071e3;
  flex: 1;
  min-width: 100px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.weather-summary {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.weather-summary h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #333;
}

#weather-summary-text {
  font-size: 14px;
  line-height: 1.5;
  color: #444;
}

.daily-weather-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.daily-weather-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
}

.day-header {
  background-color: #0071e3;
  color: white;
  padding: 8px;
  text-align: center;
  font-weight: 500;
}

.day-date {
  font-size: 18px;
  font-weight: 600;
  padding: 8px;
  text-align: center;
  background-color: #f0f0f0;
}

.weather-icon {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  padding: 10px;
}

.sunny {
  background-color: #ffeb3b;
  color: #ff9800;
}

.partly-cloudy {
  background-color: #e3f2fd;
  color: #2196f3;
}

.cloudy {
  background-color: #eceff1;
  color: #607d8b;
}

.rainy {
  background-color: #e1f5fe;
  color: #03a9f4;
}

.stormy {
  background-color: #ede7f6;
  color: #673ab7;
}

.weather-stats-daily {
  padding: 10px;
}

.weather-stat-daily {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
}

.stat-label-daily {
  color: #666;
}

.stat-value-daily {
  font-weight: 600;
  color: #333;
}

/* Style pour mettre en valeur les aspects de l'astre maître */
@keyframes masterHighlightPulse {
  0% { background-color: rgba(255, 215, 0, 0.2); }
  50% { background-color: rgba(255, 215, 0, 0.4); }
  100% { background-color: rgba(255, 215, 0, 0.2); }
}

.master-highlight {
  background-color: rgba(255, 215, 0, 0.2);
  border-radius: 4px;
  padding: 2px 4px;
  border-left: 3px solid gold;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: masterHighlightPulse 2s infinite;
  position: relative;
}

.master-highlight::after {
  content: "★";
  position: absolute;
  right: 2px;
  top: -2px;
  font-size: 10px;
  color: gold;
}

.master-highlight .stat-label-daily {
  color: #333;
  font-weight: 500;
}

.master-highlight .stat-value-daily {
  color: #000;
  font-weight: 700;
}

.day-card-actions {
  display: flex;
  gap: 5px;
  padding: 8px;
}

.view-details-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  flex: 1;
  transition: background-color 0.3s;
}

.view-details-btn:hover {
  background-color: #005bb5;
}

.export-day-gcal-btn {
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.export-day-gcal-btn:hover {
  background-color: #3367D6;
}

/* Modal styles for aspect details */
.aspect-details-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  overflow-y: auto;
}

/* Style spécifique pour la fenêtre d'édition d'aspect */
.edit-aspect-modal {
  z-index: 1100; /* Z-index plus élevé pour apparaître au-dessus des autres modales */
}

.aspect-details-content {
  background-color: white;
  margin: 50px auto;
  padding: 20px;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
}

.aspect-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.aspect-details-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.aspect-details-close {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.aspect-details-close:hover {
  background-color: #0062cc;
}



.aspect-details-summary {
  background-color: #f5f5f7;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.aspect-details-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.aspect-details-item {
  display: block;
  padding: 0;
  margin-bottom: 8px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.aspect-details-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.aspect-details-item.positive {
  border-left: 4px solid #4caf50;
}

.aspect-details-item.negative {
  border-left: 4px solid #f44336;
}

.aspect-details-item.neutral {
  border-left: 4px solid #9e9e9e;
  padding: 10px;
}

.aspect-details-item.master {
  background-color: rgba(255, 235, 59, 0.1);
}

.aspect-details-planets {
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 10px;
  color: #333;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Checkbox styles for aspect selection */
.aspect-checkbox-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.aspect-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.small-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.small-btn:hover {
  background-color: #0062cc;
}

.aspect-checkbox-label {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.aspect-checkbox-label:hover {
  background-color: #e8e8e8;
}

.aspect-checkbox-label input {
  margin-right: 5px;
}

.aspect-checkbox-label .aspect-name {
  flex: 1;
}

.aspect-checkbox-label .edit-aspect-btn {
  background: none;
  border: none;
  font-size: 14px;
  color: #999;
  cursor: pointer;
  padding: 0 5px;
  opacity: 0.5;
  transition: opacity 0.2s, color 0.2s;
}

.aspect-checkbox-label:hover .edit-aspect-btn {
  opacity: 1;
}

.aspect-checkbox-label .edit-aspect-btn:hover {
  color: var(--primary-color);
}

.aspect-checkbox-label.positive {
  border-left: 3px solid #4caf50;
}

.aspect-checkbox-label.negative {
  border-left: 3px solid #f44336;
}

/* Icon customization section */
.icon-customization-section {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  margin-top: 16px;
}

.icon-customization-title {
  font-weight: 500;
  margin-bottom: 8px;
}

.icon-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 5px;
}

.icon-preview {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border-radius: 50%;
}

/* Weather thresholds */
.weather-thresholds {
  margin-top: 16px;
  border-top: 1px solid #e0e0e0;
  padding-top: 12px;
}

.threshold-title {
  font-weight: 500;
  margin-bottom: 8px;
}

.threshold-group {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 5px;
}

.threshold-group label {
  min-width: 120px;
  margin-bottom: 0;
}

.threshold-group input[type="number"] {
  width: 50px;
  padding: 4px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
}

/* Custom aspects section */
.custom-aspects-section {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 8px;
  margin-top: 16px;
}

.custom-aspects-title {
  font-weight: 500;
  margin-bottom: 12px;
}

.custom-aspect-form {
  background-color: #fff;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.custom-aspect-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.custom-aspect-input-group {
  flex: 1;
  min-width: 120px;
}

.custom-aspect-input-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 13px;
}

.custom-aspect-input-group input,
.custom-aspect-input-group select {
  width: 100%;
  padding: 6px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

#add-custom-aspect-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
}

#add-custom-aspect-btn:hover {
  background-color: #0062cc;
}

.custom-aspects-list {
  max-height: 200px;
  overflow-y: auto;
}

.custom-aspect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.custom-aspect-item.positive {
  border-left: 3px solid #4caf50;
}

.custom-aspect-item.negative {
  border-left: 3px solid #f44336;
}

.custom-aspect-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-aspect-angle {
  font-weight: 500;
}

.custom-aspect-name {
  color: #666;
}

.custom-aspect-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #f0f0f0;
}

.custom-aspect-type.positive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.custom-aspect-type.negative {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.custom-aspect-actions {
  display: flex;
  gap: 8px;
}

.custom-aspect-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #999;
  transition: color 0.2s;
}

.custom-aspect-action-btn:hover {
  color: #333;
}

.custom-aspect-action-btn.delete:hover {
  color: #f44336;
}

.custom-aspect-action-btn.edit:hover {
  color: #2196f3;
}

.custom-aspect-action-btn.toggle:hover {
  color: #ff9800;
}

/* Edit aspect modal styles */
.edit-aspect-form {
  padding: 10px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-family: var(--font-family);
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}

.primary-btn:hover {
  background-color: #0062cc;
}

.secondary-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}

.secondary-btn:hover {
  background-color: #e0e0e0;
}

/* Manage aspects modal styles */
.manage-aspects-content {
  max-width: 800px;
  width: 90%;
}

.manage-aspects-description {
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
  background-color: #f5f5f7;
  padding: 12px;
  border-radius: 8px;
}

.manage-aspects-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.manage-aspect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.manage-aspect-item.positive {
  border-left: 3px solid #4caf50;
}

.manage-aspect-item.negative {
  border-left: 3px solid #f44336;
}

.manage-aspect-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.manage-aspect-angle {
  font-weight: 600;
  min-width: 50px;
}

.manage-aspect-name {
  font-weight: 500;
}

.manage-aspect-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
}

.manage-aspect-type.positive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.manage-aspect-type.negative {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.manage-aspect-actions {
  display: flex;
  gap: 8px;
}

.manage-aspect-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  transition: color 0.2s;
}

.manage-aspect-btn:hover {
  color: #333;
}

.manage-aspect-btn.edit:hover {
  color: var(--primary-color);
}

.manage-aspect-btn.delete:hover {
  color: #f44336;
}

.manage-aspect-btn.toggle:hover {
  color: #ff9800;
}

.manage-aspect-btn.toggle-active {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.manage-aspect-btn.toggle-active.active {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.manage-aspect-btn.toggle-active.inactive {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.manage-aspect-btn.toggle-active:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.manage-aspects-actions {
  text-align: center;
  margin-top: 16px;
}

.weather-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

/* Section pour l'importation d'événements */
.events-import-section {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
}

.events-import-section h2 {
  text-align: center;
  margin-bottom: 8px;
}

.tool-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 0;
  margin-bottom: 16px;
}

.csv-tool-link, .search-events-link {
  text-align: center;
  font-size: 14px;
}

.csv-tool-link a, .search-events-link a {
  color: #0071e3;
  text-decoration: none;
}

.csv-tool-link a:hover, .search-events-link a:hover {
  text-decoration: underline;
}

.events-import-section h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
}

.csv-import {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.csv-import label {
  font-size: 14px;
  color: var(--secondary-color);
}

.csv-import textarea {
  width: 100%;
  height: 100px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 14px;
  resize: vertical;
}

.events-table-container {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

#events-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

#events-table th,
#events-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

#events-table th {
  font-weight: 600;
  color: var(--text-color);
  background-color: #f9f9f9;
  position: sticky;
  top: 0;
}

#events-table tr:hover {
  background-color: #f5f5f7;
}

.events-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.events-actions button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.event-action-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.astral-positions-results {
  margin-top: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.view-toggle {
  display: flex;
  gap: 10px;
}

.toggle-btn {
  background-color: #6c5ce7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggle-btn:hover {
  background-color: #5b4bc9;
}

.toggle-btn.active {
  background-color: #5b4bc9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.export-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.export-btn:hover {
  background-color: #45a049;
}

.export-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Styles pour la vue en règlette */
.events-ruler-view {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ruler-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.ruler-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ruler-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.ruler-buttons {
  display: flex;
  gap: 8px;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

.toggle-mode-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-mode-btn:hover {
  background-color: #0b7dda;
}

.toggle-mode-btn.active {
  background-color: #4CAF50;
}

/* Styles pour la règlette 30° */
.ruler-30deg .ruler-scale {
  height: 80px;
  margin-top: 10px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.ruler-30deg .zodiac-signs-container {
  display: none;
}

.ruler-30deg .ruler-marks {
  height: 100%;
}

.ruler-30deg .ruler-mark {
  height: 100%;
  border-right-color: #ddd;
}

.ruler-30deg .ruler-mark-label {
  font-weight: bold;
  color: #333;
}

.ruler-30deg .planet-marker {
  font-size: 18px;
  top: 25px;
}

.ruler-30deg .planet-name {
  top: -20px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid #ddd;
  font-size: 10px;
}

.ruler-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 10px;
}

.ruler-legend-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #555;
}

.planet-symbol {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 5px;
  text-align: center;
  font-size: 16px;
  line-height: 18px;
}

.ruler-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.event-ruler {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.event-ruler-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.event-ruler-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.event-ruler-date {
  font-size: 12px;
  color: #666;
}

.ruler-scale {
  position: relative;
  height: 60px;
  background-color: #f5f5f7;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.ruler-marks {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
}

.ruler-mark {
  flex: 1;
  border-right: 1px solid #ddd;
  position: relative;
  height: 100%;
}

.ruler-mark:nth-child(30n) {
  border-right: 2px solid #999;
  background-color: rgba(0, 0, 0, 0.03);
}

.ruler-mark-label {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 9px;
  color: #666;
}

.zodiac-signs-container {
  display: flex;
  margin-bottom: 5px;
  height: 20px;
  position: relative;
}

.zodiac-sign-label {
  position: absolute;
  top: 0;
  width: 8.333%; /* 100% / 12 signes */
  text-align: center;
  font-size: 11px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.planet-marker {
  position: absolute;
  font-size: 16px;
  top: 20px;
  transform: translateX(-50%);
  z-index: 2;
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
}

.planet-name {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 9px;
  font-weight: 500;
  white-space: nowrap;
  color: #333;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 1px 3px;
  border-radius: 3px;
  display: none;
}

.planet-marker:hover .planet-name {
  display: block;
}

#events-with-positions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.event-with-positions {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  border-left: 3px solid var(--primary-color);
}

.event-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.event-title {
  font-weight: 600;
  font-size: 14px;
}

.event-datetime {
  color: var(--secondary-color);
  font-size: 13px;
}

.event-description {
  font-size: 13px;
  margin-bottom: 10px;
  color: var(--secondary-color);
}

.event-positions {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
}

.event-planet {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.event-planet-symbol {
  margin-right: 4px;
  font-size: 14px;
}

/* Planet symbols */
.planet-symbol {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 6px;
  text-align: center;
  font-size: 16px;
}

/* Zodiac sign styling */
.sign {
  font-weight: 500;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Styles pour la fiche d'aspect */
.aspect-card-content {
  max-width: 500px; /* Largeur maximale réduite */
  width: 90%;
  position: relative;
  z-index: 10000; /* Z-index plus élevé que la modale */
  margin: 50px auto; /* Centrer horizontalement et ajouter une marge en haut */
  background-color: white; /* Fond blanc */
  border-radius: 12px; /* Coins arrondis */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5); /* Ombre plus prononcée */
}

#aspect-card-modal {
  z-index: 9999; /* S'assurer que la fiche d'aspect est au-dessus de tout */
  display: none; /* Caché par défaut */
  position: fixed; /* Position fixe */
  top: 0; /* En haut de la page */
  left: 0; /* À gauche de la page */
  width: 100%; /* Largeur complète */
  height: 100%; /* Hauteur complète */
  background-color: rgba(0, 0, 0, 0.8); /* Fond semi-transparent plus foncé */
  overflow-y: auto; /* Permettre le défilement si nécessaire */
}

.aspect-card-container-with-history {
  display: flex;
  gap: 20px;
  padding: 16px;
}

.aspect-card-left-panel, .aspect-card-right-panel {
  flex: 1;
}

.aspect-card-left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.aspect-card-right-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-left: 1px solid #eee;
  padding-left: 20px;
}

.aspect-card-right-panel h3 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
}

.aspect-importance-simple, .aspect-comment-simple {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.aspect-importance-simple label, .aspect-comment-simple label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.aspect-history-list {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.aspect-history-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  position: relative;
}

.aspect-history-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.aspect-history-date {
  font-weight: 600;
  color: #333;
}

.aspect-history-rating {
  color: #4a69bd;
  font-weight: 500;
}

.aspect-history-comment {
  font-size: 13px;
  color: #555;
  margin-bottom: 10px;
  line-height: 1.4;
}

.aspect-history-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.history-edit-btn, .history-delete-btn {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  border: none;
  color: white;
  font-weight: 500;
}

.history-edit-btn {
  background-color: #6c5ce7;
}

.history-edit-btn:hover {
  background-color: #5b4bc9;
}

.history-delete-btn {
  background-color: #e74c3c;
}

.history-delete-btn:hover {
  background-color: #c0392b;
}

#aspect-comment {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  font-family: inherit;
  resize: vertical;
}

#aspect-importance {
  width: 80px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

#aspect-rating {
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.aspect-history-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  background-color: white;
}

.aspect-history-item {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.aspect-history-item:last-child {
  border-bottom: none;
}

.aspect-history-date {
  font-weight: bold;
  color: #333;
}

.aspect-history-rating {
  display: inline-block;
  margin-left: 8px;
  color: #0071e3;
}

.aspect-history-comment {
  margin-top: 4px;
  font-size: 13px;
  color: #555;
}

/* Suppression des styles pour les dates futures qui ne sont plus utilisées */

.aspect-card-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.edit-btn, .delete-btn {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.edit-btn {
  background-color: #6c5ce7;
  color: white;
  border: none;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
}

.view-aspect-card-btn {
  background-color: #4a69bd; /* Nouvelle couleur bleue */
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px; /* Padding légèrement plus grand */
  font-size: 13px; /* Police légèrement plus grande */
  margin-right: 10px;
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease; /* Transition fluide */
}

.view-aspect-card-btn:hover {
  background-color: #1e3799; /* Bleu plus foncé au survol */
  transform: translateY(-2px); /* Effet de soulèvement plus prononcé */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Ombre plus grande */
}

/* Styles pour la fenêtre modale d'interprétation */
.interpretation-content {
  max-width: 600px;
  width: 90%;
}

.interpretation-body {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.interpretation-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 16px;
}

.interpretation-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.interpretation-edit-text {
  width: 100%;
  min-height: 200px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.interpretation-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.edit-btn, .save-btn, .cancel-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  border: none;
}

.edit-btn {
  background-color: #6c5ce7;
  color: white;
}

.edit-btn:hover {
  background-color: #5b4bc9;
}

.save-btn {
  background-color: #4caf50;
  color: white;
}

.save-btn:hover {
  background-color: #43a047;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

/* Bouton d'interprétation dans les aspects */
.interpret-aspect-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  cursor: pointer;
  margin-left: 8px;
  width: auto;
  max-width: 70px;
  min-width: 50px;
}

.interpret-aspect-btn:hover {
  background-color: #0062cc;
}

/* Modal styles for interpretations */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: #fefefe;
  margin: 5% auto;
  padding: 0;
  border: 1px solid #888;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
  background-color: #4CAF50;
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.close {
  color: white;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  line-height: 1;
}

.close:hover {
  opacity: 0.7;
}

.modal-body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.interpretation-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.interpretation-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  font-size: 16px;
}

.interpretation-text {
  line-height: 1.6;
  color: #555;
}

/* Time Navigation Box Styles - Minimal */
.time-navigation-box {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px;
  margin: 10px 0;
}

.time-nav-minimal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.time-nav-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.time-nav-label {
  font-size: 11px;
  color: #666;
  font-weight: bold;
  min-width: 40px;
}

.time-nav-btn-mini {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 20px;
  height: 20px;
}

.time-nav-btn-mini:hover {
  background-color: #0056b3;
}

.time-nav-btn-mini:active {
  background-color: #004085;
  transform: translateY(1px);
}

.time-nav-reset-mini {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 22px;
}

.time-nav-reset-mini:hover {
  background-color: #218838;
}

.time-nav-reset-mini:active {
  background-color: #1e7e34;
  transform: translateY(1px);
}

/* Layout en deux colonnes */
.main-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-top: 20px;
}

.zodiac-column {
  flex: 2;
  min-width: 0;
}

.controls-column {
  flex: 1;
  min-width: 300px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.controls-column .date-inputs {
  margin: 0;
}

/* Responsive design */
@media (max-width: 1200px) {
  .main-layout {
    flex-direction: column;
  }

  .controls-column {
    position: static;
    max-height: none;
    order: -1;
  }

  .zodiac-column {
    order: 1;
  }
}

/* Boutons de légende et carte 360° */
.legend-button-container {
  text-align: center;
  margin: 15px 0;
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.legend-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  width: auto;
}

.legend-btn:hover {
  background-color: #5a6268;
}

.legend-btn:active {
  background-color: #495057;
  transform: translateY(1px);
}

.chart-360-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  width: auto;
}

.chart-360-btn:hover {
  background-color: #218838;
}

.chart-360-btn:active {
  background-color: #1e7e34;
  transform: translateY(1px);
}

.multi-transit-btn {
  background-color: #6f42c1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  width: auto;
}

.multi-transit-btn:hover {
  background-color: #5a32a3;
}

.multi-transit-btn:active {
  background-color: #4c2a85;
  transform: translateY(1px);
}



/* Section de navigation par position planétaire */
.planet-position-navigation {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.planet-position-navigation h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
  text-align: center;
}

.planet-nav-info {
  margin-bottom: 15px;
  text-align: center;
}

.planet-nav-info p {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
  line-height: 1.4;
}

.planet-nav-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.planet-nav-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
  min-width: 120px;
  justify-content: center;
}

.planet-nav-btn:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.planet-nav-btn:active:not(:disabled) {
  background-color: #004085;
  transform: translateY(0);
}

.planet-nav-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.planet-nav-details {
  background-color: #e9ecef;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
}

.current-position-info {
  font-size: 13px;
  color: #495057;
}

.current-position-info strong {
  color: #212529;
}

/* Tableau des dates planétaires */
.planet-dates-table-container {
  margin-top: 20px;
  background-color: var(--card-background);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.planet-dates-table-container h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 12px;
  text-align: center;
}

.dates-table-wrapper {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.planet-dates-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  background-color: white;
}

.planet-dates-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 8px 6px;
  text-align: center;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.planet-dates-table td {
  padding: 6px 4px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.planet-dates-table tbody tr:hover {
  background-color: #f8f9fa;
}

.date-type-past {
  color: #6c757d;
  font-style: italic;
}

.date-type-future {
  color: #28a745;
  font-weight: 500;
}

.date-type-current {
  color: var(--primary-color);
  font-weight: 600;
}

.planet-date-cell {
  font-weight: 500;
}

.planet-time-cell {
  font-family: monospace;
  font-size: 11px;
}

.planet-position-cell {
  font-weight: 500;
  color: #495057;
}

.goto-date-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.goto-date-btn:hover {
  background-color: #0062cc;
}

.goto-date-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.table-loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

.table-loading p {
  margin: 0;
}

/* Responsive pour le tableau */
@media (max-width: 768px) {
  .planet-dates-table {
    font-size: 11px;
  }

  .planet-dates-table th,
  .planet-dates-table td {
    padding: 6px 3px;
  }

  .goto-date-btn {
    padding: 3px 6px;
    font-size: 9px;
  }
}

/* Section des aspects en dessous du zodiaque */
.zodiac-aspects-section-below {
  width: 100%;
  margin-top: 20px;
  background-color: var(--card-background);
  padding: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.zodiac-aspects-section-below h4 {
  font-size: 16px;
  margin-bottom: 12px;
  color: var(--primary-color);
  text-align: center;
  font-weight: 600;
}

.zodiac-aspects-section-below .proportional-aspects-info {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  text-align: center;
}

.zodiac-aspects-section-below .proportional-aspects-info p {
  margin: 0;
  font-size: 14px;
  color: #856404;
}

.zodiac-aspects-section-below .zodiac-aspects-filter {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.zodiac-aspects-section-below .aspect-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.zodiac-aspects-section-below .aspect-type-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 12px;
  transition: background-color 0.2s;
}

.zodiac-aspects-section-below .aspect-type-item:hover {
  background-color: #f8f9fa;
}

.zodiac-aspects-section-below .aspect-type-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin: 0;
  font-size: 13px;
  font-weight: 500;
}

.zodiac-aspects-section-below .apply-filter-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 12px;
  margin-right: 8px;
  transition: background-color 0.2s;
}

.zodiac-aspects-section-below .apply-filter-btn:hover {
  background-color: #0062cc;
}

.zodiac-aspects-section-below .zodiac-aspects-list {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

/* Responsive pour la section en dessous du zodiaque */
@media (max-width: 768px) {
  .zodiac-aspects-section-below {
    margin-top: 16px;
    padding: 12px;
  }

  .zodiac-aspects-section-below .aspect-types-grid {
    grid-template-columns: 1fr;
  }

  .zodiac-aspects-section-below .apply-filter-btn {
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
  }
}

/* Section d'onglets avec tableau mensuel des aspects */
.monthly-aspects-section {
  width: 100%;
  margin-top: 20px;
  margin-bottom: 16px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

/* Header avec onglets et contrôles */
.monthly-aspects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

/* Onglets */
.monthly-aspects-tabs {
  display: flex;
}

.monthly-tab-btn {
  flex: 1;
  background-color: transparent;
  border: none;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.monthly-tab-btn:hover {
  background-color: #e9ecef;
  color: #495057;
}

.monthly-tab-btn.active {
  background-color: white;
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* Contrôles de mode */
.monthly-mode-controls {
  padding: 0 16px;
}

.monthly-360-mode-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
}

.monthly-360-mode-btn:hover {
  background: linear-gradient(135deg, #218838, #1ea080);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.4);
}

.monthly-360-mode-btn.active {
  background: linear-gradient(135deg, #dc3545, #c82333);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.monthly-360-mode-btn.active:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  box-shadow: 0 3px 6px rgba(220, 53, 69, 0.4);
}

.monthly-transit-mode-btn {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.monthly-transit-mode-btn:hover {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(111, 66, 193, 0.4);
}

.monthly-transit-mode-btn.active {
  background: linear-gradient(135deg, #28a745, #20c997);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.monthly-transit-mode-btn.active:hover {
  background: linear-gradient(135deg, #218838, #1ea080);
  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.4);
}

/* Contenu des onglets */
.monthly-aspects-content {
  padding: 16px;
}

.monthly-tab-content {
  display: none;
}

.monthly-tab-content.active {
  display: block;
}

.monthly-table-header {
  margin-bottom: 16px;
  text-align: center;
}

.monthly-table-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.monthly-table-description {
  font-size: 13px;
  color: #6c757d;
  margin: 0 0 16px 0;
  font-style: italic;
}

/* Section de filtres mensuels */
.monthly-filters-section {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.monthly-filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.monthly-filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.monthly-filter-label {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

/* Types d'aspects */
.monthly-aspect-types {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.monthly-checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #495057;
  cursor: pointer;
  user-select: none;
  transition: color 0.2s;
}

.monthly-checkbox-label:hover {
  color: var(--primary-color);
}

.monthly-checkbox-label input[type="checkbox"] {
  display: none;
}

.monthly-checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #dee2e6;
  border-radius: 3px;
  background-color: white;
  transition: all 0.2s;
  position: relative;
}

.monthly-checkbox-label input[type="checkbox"]:checked + .monthly-checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.monthly-checkbox-label input[type="checkbox"]:checked + .monthly-checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Contrôle d'orbe */
.monthly-orb-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

#monthly-orb-filter,
#monthly-orb-filter-next,
#monthly-orb-filter-prev {
  width: 100px;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
}

#monthly-orb-filter::-webkit-slider-thumb,
#monthly-orb-filter-next::-webkit-slider-thumb,
#monthly-orb-filter-prev::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#monthly-orb-filter::-moz-range-thumb,
#monthly-orb-filter-next::-moz-range-thumb,
#monthly-orb-filter-prev::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#monthly-orb-value,
#monthly-orb-value-next,
#monthly-orb-value-prev {
  font-size: 13px;
  font-weight: 600;
  color: var(--primary-color);
  min-width: 35px;
  text-align: center;
}

/* Bouton d'application */
.monthly-apply-filter-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 20px;
}

.monthly-apply-filter-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.monthly-apply-filter-btn:active {
  transform: translateY(0);
}

/* Tableau mensuel */
.monthly-table-wrapper {
  overflow-x: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: white;
}

.monthly-aspects-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  min-width: 800px;
}

.monthly-aspects-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 8px 4px;
  text-align: center;
  border-bottom: 2px solid #dee2e6;
  border-right: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.monthly-aspects-table th:first-child {
  background-color: #e9ecef;
  color: #495057;
  font-weight: 700;
  text-align: left;
  padding-left: 8px;
  min-width: 80px;
  position: sticky;
  left: 0;
  z-index: 11;
}

.monthly-aspects-table td {
  padding: 6px 4px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  vertical-align: middle;
  position: relative;
}

.monthly-aspects-table td:first-child {
  background-color: #f8f9fa;
  font-weight: 600;
  text-align: left;
  padding-left: 8px;
  color: #495057;
  position: sticky;
  left: 0;
  z-index: 5;
  border-right: 1px solid #dee2e6;
}

.monthly-aspects-table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Cellules de jours cliquables */
.day-cell {
  transition: all 0.2s;
  font-weight: 500;
}

.day-cell.current-day {
  background-color: var(--primary-color);
  color: white;
  font-weight: 700;
}

.day-cell.current-day:hover {
  background-color: #0062cc;
}

/* En-têtes de jours cliquables */
.day-header-clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.day-header-clickable:hover {
  background-color: rgba(0, 123, 255, 0.2);
  color: var(--primary-color);
  transform: scale(1.05);
}

/* Cellules de résumé éditables */
.summary-editable {
  cursor: text;
  font-weight: 600;
  background-color: #fff8e1;
  border: 1px solid #ffcc02;
  transition: all 0.2s;
}

.summary-editable:hover {
  background-color: #fff3c4;
  border-color: #ff9800;
}

.summary-editable:focus {
  outline: none;
  background-color: #ffffff;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

/* Cellules de prédictions automatiques */
.predicted-value {
  font-weight: 700;
  cursor: default;
  border: 2px solid;
  position: relative;
  transition: all 0.3s ease;
}

.predicted-value::before {
  content: '🔮';
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 8px;
  opacity: 0.7;
}

/* Niveaux de confiance des prédictions */
.predicted-value.high-confidence {
  background-color: #e8f5e8;
  border-color: #28a745;
  color: #155724;
}

.predicted-value.medium-confidence {
  background-color: #fff3cd;
  border-color: #ffc107;
  color: #856404;
}

.predicted-value.low-confidence {
  background-color: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.predicted-value:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  cursor: pointer;
}

/* Popup de détails des prédictions */
.prediction-details-popup {
  position: fixed;
  background: white;
  border: 2px solid #007bff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
  max-width: 500px;
  max-height: 600px;
  overflow-y: auto;
  z-index: 10000;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.prediction-details-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 15px 20px;
  border-radius: 10px 10px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prediction-details-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-popup-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-popup-btn:hover {
  background-color: rgba(255,255,255,0.2);
}

.prediction-details-content {
  padding: 20px;
  line-height: 1.5;
}

.prediction-details-content h4 {
  color: #333;
  margin: 20px 0 10px 0;
  font-size: 16px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 5px;
}

.prediction-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.prediction-value, .prediction-confidence {
  margin: 8px 0;
  font-size: 14px;
}

.calculation-method {
  background: #e3f2fd;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.aspects-list {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
}

.aspect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.aspect-item:last-child {
  border-bottom: none;
}

.aspect-planets {
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.aspect-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  margin: 0 8px;
}

.aspect-type.conjunction {
  background: #fff3cd;
  color: #856404;
}

.aspect-type.opposition {
  background: #f8d7da;
  color: #721c24;
}

.aspect-type.trine {
  background: #d4edda;
  color: #155724;
}

.aspect-type.square {
  background: #f5c6cb;
  color: #721c24;
}

.aspect-type.sextile {
  background: #cce5ff;
  color: #004085;
}

.aspect-type.victoire-martial {
  background: #e1bee7;
  color: #4a148c;
}

.aspect-type.trigone-2 {
  background: #ffccbc;
  color: #bf360c;
}

.aspect-type.trigone-3 {
  background: #cfd8dc;
  color: #263238;
}

.aspect-orb {
  font-size: 12px;
  color: #6c757d;
  min-width: 40px;
  text-align: right;
}

.score-items {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
}

.score-item:last-child {
  border-bottom: none;
}

.score-total {
  display: flex;
  justify-content: space-between;
  padding: 10px 0 0 0;
  margin-top: 10px;
  border-top: 2px solid #007bff;
  font-weight: bold;
}

.score-value.positive {
  color: #28a745;
}

.score-value.negative {
  color: #dc3545;
}

.pattern-match {
  background: #e8f5e8;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.pattern-examples {
  margin-top: 10px;
}

.pattern-example {
  background: white;
  padding: 8px;
  margin: 5px 0;
  border-radius: 4px;
  border-left: 4px solid #28a745;
  font-size: 13px;
}

.aspect-signature {
  background: #f1f3f4;
  padding: 12px;
  border-radius: 6px;
}

.aspect-signature code {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  word-break: break-all;
}

.no-aspects {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

/* Styles pour les nouveaux éléments du popup */
.historical-data {
  margin: 8px 0;
  font-size: 14px;
  color: #495057;
}

.historical-aspects-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
}

.historical-aspect-item {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.historical-aspect-item:last-child {
  margin-bottom: 0;
}

.aspect-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.aspect-average {
  color: #007bff;
  font-weight: 600;
}

.historical-occurrences {
  color: #6c757d;
  font-size: 12px;
  line-height: 1.4;
}

.breakdown-list {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
}

.breakdown-item {
  margin-bottom: 15px;
  padding: 12px;
  background: #f1f3f4;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.breakdown-item:last-child {
  margin-bottom: 0;
}

.breakdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.aspect-name {
  font-weight: 600;
  color: #495057;
}

.contribution {
  color: #28a745;
  font-weight: 600;
}

.breakdown-details {
  font-size: 13px;
}

.breakdown-stats {
  margin-bottom: 8px;
  color: #6c757d;
}

.breakdown-stats span {
  margin-right: 15px;
}

.occurrence-details {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.occurrence-tag {
  background: #e9ecef;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  color: #495057;
  border: 1px solid #ced4da;
}

.calculation-summary {
  background: #e8f5e8;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.calc-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #d4edda;
}

.calc-item:last-child {
  border-bottom: none;
}

.no-breakdown {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

/* Cellules de noms des lignes de résumé */
.summary-row-name-cell {
  background-color: #e3f2fd !important;
  font-weight: 700;
  text-align: left;
  padding-left: 8px;
  color: #1976d2;
  position: sticky;
  left: 0;
  z-index: 5;
  border-right: 1px solid #1976d2;
  border-bottom: 1px solid #1976d2;
}

/* Cellules avec aspects */
.aspect-count {
  font-weight: 600;
  color: #495057;
}

.aspect-count.high-count {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.aspect-count.medium-count {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.aspect-count.low-count {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

/* Cellules vides */
.empty-cell {
  color: #dee2e6;
}

/* Cellules d'aspects cliquables pour voir les détails */
.aspect-detail-clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.aspect-detail-clickable:hover {
  background-color: rgba(0, 123, 255, 0.1);
  transform: scale(1.1);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modal des détails des aspects quotidiens */
.daily-aspects-info {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid var(--primary-color);
}

.daily-aspects-info p {
  margin: 0;
  font-size: 14px;
  color: #495057;
}

.daily-aspects-table-wrapper {
  overflow-x: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: white;
}

.daily-aspects-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  min-width: 700px;
}

.daily-aspects-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 12px 8px;
  text-align: center;
  border-bottom: 2px solid #dee2e6;
  border-right: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.daily-aspects-table th:last-child {
  border-right: none;
}

.daily-aspects-table td {
  padding: 10px 8px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  vertical-align: middle;
}

.daily-aspects-table td:last-child {
  border-right: none;
}

.daily-aspects-table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Colonnes spécifiques */
.daily-aspects-table .transit-planet {
  font-weight: 600;
  color: #dc3545;
}

.daily-aspects-table .natal-planet {
  font-weight: 600;
  color: #28a745;
}

.daily-aspects-table .aspect-type {
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
}

.daily-aspects-table .aspect-conjunction {
  background-color: #FF0000;
}

.daily-aspects-table .aspect-sextile {
  background-color: #00AA00;
}

.daily-aspects-table .aspect-square {
  background-color: #FF6600;
}

.daily-aspects-table .aspect-trine {
  background-color: #0066FF;
}

.daily-aspects-table .aspect-opposition {
  background-color: #000000;
}

.daily-aspects-table .aspect-victoire-martial {
  background-color: #9C27B0;
  color: white;
}

.daily-aspects-table .aspect-trigone-2 {
  background-color: #FF5722;
  color: white;
}

.daily-aspects-table .aspect-trigone-3 {
  background-color: #607D8B;
  color: white;
}

.daily-aspects-table .orb-value {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #6c757d;
}

.daily-aspects-table .position-value {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #495057;
}

#no-aspects-message {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-style: italic;
}

#no-aspects-message p {
  margin: 0;
  font-size: 16px;
}

/* Styles pour la section des changements de secteurs */
.sector-changes-separator {
  background-color: #e9ecef !important;
  color: #495057 !important;
  font-weight: 700 !important;
  text-align: center !important;
  padding: 12px 8px !important;
  border-top: 2px solid #007bff !important;
  border-bottom: 1px solid #dee2e6 !important;
}

.sector-change-cell {
  font-size: 10px !important;
  padding: 4px 2px !important;
  text-align: center !important;
  vertical-align: middle !important;
}

.sector-change-detected {
  background-color: #fff3cd !important;
  color: #856404 !important;
  font-weight: 600 !important;
  border: 1px solid #ffeaa7 !important;
  border-radius: 3px !important;
}

.sector-change-detected:hover {
  background-color: #ffeaa7 !important;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  cursor: pointer;
}

/* Responsive pour le tableau mensuel */
@media (max-width: 768px) {
  .monthly-aspects-section {
    margin-top: 16px;
  }

  .monthly-aspects-content {
    padding: 12px;
  }

  .monthly-tab-btn {
    padding: 10px 12px;
    font-size: 13px;
  }

  .monthly-aspects-table {
    font-size: 10px;
    min-width: 600px;
  }

  .monthly-aspects-table th,
  .monthly-aspects-table td {
    padding: 4px 2px;
  }

  .monthly-aspects-table th:first-child,
  .monthly-aspects-table td:first-child {
    min-width: 60px;
    padding-left: 6px;
  }

  .daily-aspects-table {
    font-size: 11px;
    min-width: 500px;
  }

  .daily-aspects-table th,
  .daily-aspects-table td {
    padding: 8px 4px;
  }
}

/* Mini calendrier minimaliste */
.mini-calendar-container {
  margin-top: 16px;
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.mini-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.mini-calendar-nav {
  background: none;
  border: none;
  font-size: 18px;
  font-weight: bold;
  color: var(--primary-color);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mini-calendar-nav:hover {
  background-color: rgba(0, 123, 255, 0.1);
  transform: scale(1.1);
}

.mini-calendar-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  flex: 1;
}

.mini-calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  font-size: 11px;
}

.mini-calendar-day-header {
  text-align: center;
  font-weight: 600;
  color: #6c757d;
  padding: 4px 2px;
  font-size: 10px;
}

.mini-calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  color: #495057;
  background-color: transparent;
  border: 1px solid transparent;
  min-height: 28px;
  flex-direction: column;
  padding: 2px;
  position: relative;
}

.mini-calendar-day .day-number {
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
}

.mini-calendar-day .day-importance-icons,
.mini-calendar-day .day-surprise-icons {
  display: block;
  text-align: center;
  line-height: 1;
  height: 7px;
  margin-top: 1px;
  overflow: hidden;
}

.mini-calendar-day .day-importance-icons {
  /* Étoiles dorées sur la première ligne */
  font-size: 6px;
  margin-top: 1px;
  letter-spacing: -0.5px;
}

.mini-calendar-day .day-surprise-icons {
  /* Emojis surprise sur la deuxième ligne */
  font-size: 5px;
  margin-top: 0px;
  letter-spacing: -0.5px;
  line-height: 0.8;
}

.mini-calendar-day.has-border {
  border-width: 2px;
  border-style: solid;
}

.mini-calendar-day:hover {
  background-color: rgba(0, 123, 255, 0.1);
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.mini-calendar-day.other-month {
  color: #ced4da;
  font-weight: 400;
}

.mini-calendar-day.current-day {
  background-color: var(--primary-color);
  color: white;
  font-weight: 700;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.mini-calendar-day.current-day:hover {
  background-color: #0056b3;
  transform: scale(1.1);
}

.mini-calendar-day.selected-day {
  background-color: #28a745;
  color: white;
  font-weight: 700;
}

.mini-calendar-day.selected-day:hover {
  background-color: #218838;
}

/* Responsive pour le mini calendrier */
@media (max-width: 768px) {
  .mini-calendar-container {
    padding: 8px;
    margin-top: 12px;
  }

  .mini-calendar-grid {
    gap: 1px;
    font-size: 10px;
  }

  .mini-calendar-day {
    min-height: 24px;
  }

  .mini-calendar-title {
    font-size: 13px;
  }

  .mini-calendar-nav {
    width: 24px;
    height: 24px;
    font-size: 16px;
  }
}

/* Menu contextuel pour personnaliser les couleurs des jours */
.day-color-context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  display: none;
  min-width: 300px;
  max-width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.context-menu-header {
  background: var(--primary-color);
  color: white;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  font-size: 14px;
}

.context-menu-content {
  padding: 16px;
}

.color-section {
  margin-bottom: 20px;
}

.color-section label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.color-picker {
  width: 50px;
  height: 35px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  padding: 0;
}

.color-picker:hover {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.color-picker:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
}

.reset-color-button {
  width: 35px;
  height: 35px;
  border: 2px solid #6c757d;
  border-radius: 8px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-color-button:hover {
  background: #6c757d;
  color: white;
  transform: scale(1.05);
}

.color-preview {
  width: 35px;
  height: 35px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
  color: black;
}

#background-color-preview {
  background: #ffffff;
}

#text-color-preview {
  background: white;
  color: #000000;
}

#border-color-preview {
  background: white;
  border: 3px solid #000000;
}

.icons-section {
  margin-bottom: 20px;
}

.icons-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.icon-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.icon-option {
  width: 35px;
  height: 35px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  background: #f8f9fa;
  font-size: 8px;
  user-select: none;
  overflow: hidden;
  line-height: 1;
}

.icon-option:hover {
  border-color: var(--primary-color);
  background: #e3f2fd;
  transform: scale(1.05);
}

.icon-option.selected {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.icon-option[data-importance="high"].selected {
  background: #fd7e14;
  border-color: #fd7e14;
}

.icon-option[data-importance="medium"].selected {
  background: #ffc107;
  border-color: #ffc107;
  color: #000;
}

.icon-option[data-importance="low"].selected {
  background: #28a745;
  border-color: #28a745;
}

.icon-option[data-surprise="high"].selected {
  background: #6f42c1;
  border-color: #6f42c1;
}

.icon-option[data-surprise="medium"].selected {
  background: #20c997;
  border-color: #20c997;
}

.icon-option[data-surprise="low"].selected {
  background: #17a2b8;
  border-color: #17a2b8;
}

/* Styles spécifiques pour les icônes d'importance */
.icon-option[data-importance] {
  font-size: 10px;
}

/* Styles spécifiques pour les icônes de surprise */
.icon-option[data-surprise] {
  font-size: 7px;
  line-height: 0.8;
}

/* Ajustement pour les icônes multiples */
.icon-option[data-importance="medium"],
.icon-option[data-importance="high"] {
  font-size: 8px;
  letter-spacing: -1px;
}

.icon-option[data-surprise="medium"],
.icon-option[data-surprise="high"] {
  font-size: 6px;
  letter-spacing: -1px;
  line-height: 0.7;
}

/* Styles pour la colorisation des secteurs */
.sector-color-picker {
  width: 40px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.condition-row label {
  font-weight: 500;
  white-space: nowrap;
}

.sector-type {
  min-width: 100px;
}

/* Styles pour la modal Multi Transit */
.multi-transit-intro {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.transit-dates-section {
  margin-bottom: 20px;
}

.transit-dates-list {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  min-height: 60px;
  padding: 10px;
  background-color: #f8f9fa;
}

.transit-date-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  border-left: 4px solid;
}

.transit-date-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.transit-color-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #dee2e6;
}

.transit-date-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.transit-date-main {
  font-weight: 600;
  color: #333;
}

.transit-date-label {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.transit-date-actions {
  display: flex;
  gap: 6px;
}

.edit-transit-btn,
.remove-transit-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.2s;
}

.edit-transit-btn {
  background-color: #007bff;
  color: white;
}

.edit-transit-btn:hover {
  background-color: #0056b3;
}

.remove-transit-btn {
  background-color: #dc3545;
  color: white;
}

.remove-transit-btn:hover {
  background-color: #c82333;
}

.add-transit-section {
  margin-bottom: 20px;
}

.transit-form {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
}

.transit-form-row {
  display: flex;
  align-items: end;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.transit-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transit-input-group label {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.transit-input {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
  width: 120px;
}

.transit-color-input {
  width: 50px;
  height: 32px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
}

.add-transit-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: background-color 0.2s;
  height: 32px;
}

.add-transit-btn:hover {
  background-color: #218838;
}

.transit-display-options {
  margin-bottom: 20px;
}

.display-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.display-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 13px;
  color: #333;
}

.display-option input[type="checkbox"] {
  margin: 0;
}

.multi-transit-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.apply-multi-transit-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.apply-multi-transit-btn:hover {
  background-color: #0056b3;
}

.clear-multi-transit-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.clear-multi-transit-btn:hover {
  background-color: #c82333;
}

.export-multi-transit-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.export-multi-transit-btn:hover {
  background-color: #5a6268;
}

.empty-transit-list {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

.day-comment-input {
  width: 100%;
  min-height: 60px;
  max-height: 120px;
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 13px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.day-comment-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.day-comment-input::placeholder {
  color: #6c757d;
  font-style: italic;
}

.comment-counter {
  text-align: right;
  font-size: 11px;
  color: #6c757d;
  margin-top: 4px;
}

.comment-counter.warning {
  color: #f57c00;
}

.comment-counter.danger {
  color: #d32f2f;
}

.context-menu-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.apply-color-btn,
.reset-color-btn,
.cancel-color-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.apply-color-btn {
  background: var(--primary-color);
  color: white;
}

.apply-color-btn:hover {
  background: #0056b3;
}

.reset-color-btn {
  background: #6c757d;
  color: white;
}

.reset-color-btn:hover {
  background: #5a6268;
}

.cancel-color-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.cancel-color-btn:hover {
  background: #e9ecef;
}

/* Styles pour les jours personnalisés */
.mini-calendar-day.custom-color {
  transition: all 0.3s ease;
}

.mini-calendar-day.has-comment {
  position: relative;
}

.mini-calendar-day.has-comment::after {
  content: '💬';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 8px;
  opacity: 0.7;
}

.day-comment-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  max-width: 200px;
  word-wrap: break-word;
  z-index: 10001;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.day-comment-tooltip.show {
  opacity: 1;
}

.day-comment-tooltip::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

/* Animation pour l'application des couleurs */
.mini-calendar-day.color-applied {
  animation: colorApplied 0.5s ease;
}

@keyframes colorApplied {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Styles pour les règles astrologiques */
.zodiac-header-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  padding: 10px 0;
  border-bottom: 2px solid #e9ecef;
}

.astrological-rules-btn {
  background: linear-gradient(135deg, #6f42c1, #8e44ad);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

.astrological-rules-btn:hover {
  background: linear-gradient(135deg, #5a2d91, #7d3c98);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.4);
}

.astrological-rules-btn:active {
  transform: translateY(0);
}

.export-data-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.export-data-btn:hover {
  background: linear-gradient(135deg, #218838, #1ea080);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.export-data-btn:active {
  transform: translateY(0);
}

.import-data-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.import-data-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.import-data-btn:active {
  transform: translateY(0);
}

/* Styles pour la modal des règles astrologiques */
.rules-intro {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  border-left: 4px solid #6f42c1;
}

.rules-intro p {
  margin: 0;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
}

.existing-rules-section {
  margin-bottom: 30px;
}

.existing-rules-section h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.rules-list {
  min-height: 60px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: #6c757d;
}

.rules-list:empty::before {
  content: "Aucune règle active. Créez votre première règle ci-dessous.";
  font-style: italic;
}

.rule-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.rule-item:hover {
  border-color: #6f42c1;
  box-shadow: 0 2px 8px rgba(111, 66, 193, 0.1);
}

.rule-description {
  flex: 1;
  font-size: 14px;
  color: #495057;
}

.rule-actions-inline {
  display: flex;
  gap: 8px;
}

.rule-toggle-btn,
.rule-edit-btn,
.rule-delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rule-toggle-btn {
  background: #28a745;
  color: white;
}

.rule-toggle-btn.disabled {
  background: #6c757d;
}

.rule-edit-btn {
  background: #ffc107;
  color: #212529;
}

.rule-delete-btn {
  background: #dc3545;
  color: white;
}

.new-rule-section {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 25px;
}

.new-rule-section h4 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.rule-condition,
.rule-effects {
  margin-bottom: 25px;
}

.rule-condition h5,
.rule-effects h5 {
  color: #6f42c1;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.condition-row label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.rule-select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #495057;
  min-width: 120px;
}

.rule-select:focus {
  outline: none;
  border-color: #6f42c1;
  box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.2);
}

.effects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.effect-group {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.2s ease;
}

.effect-group:hover {
  border-color: #6f42c1;
}

.effect-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 10px;
}

.effect-checkbox input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
}

.effect-options {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #dee2e6;
}

.effect-options label {
  display: block;
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 5px;
}

.effect-options input[type="color"],
.effect-options input[type="range"],
.effect-options select {
  width: 100%;
  padding: 6px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
}

.effect-options input[type="range"] {
  margin-bottom: 5px;
}

#planet-scale-value {
  font-size: 13px;
  color: #6f42c1;
  font-weight: 600;
}

.rule-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.add-rule-btn,
.test-rule-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.add-rule-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.add-rule-btn:hover {
  background: linear-gradient(135deg, #218838, #1ea080);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.test-rule-btn {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #212529;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.test-rule-btn:hover {
  background: linear-gradient(135deg, #e0a800, #e8630a);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.rule-preview-section {
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
  border: 1px solid #bbdefb;
  border-radius: 10px;
  padding: 20px;
}

.rule-preview-section h4 {
  color: #1976d2;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.rule-preview {
  background: white;
  border: 1px solid #e1bee7;
  border-radius: 8px;
  padding: 20px;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-style: italic;
}

/* Animations pour les effets de règles */
@keyframes sectorGlow {
  0% { box-shadow: 0 0 5px rgba(111, 66, 193, 0.5); }
  50% { box-shadow: 0 0 20px rgba(111, 66, 193, 0.8); }
  100% { box-shadow: 0 0 5px rgba(111, 66, 193, 0.5); }
}

@keyframes planetPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.sector-glow-effect {
  animation: sectorGlow 2s infinite;
}

.planet-pulse-effect {
  animation: planetPulse 1.5s infinite;
}

/* Styles pour les conditions multiples */
.condition-type-row {
  margin-bottom: 15px;
  padding: 10px;
  background: #e9ecef;
  border-radius: 6px;
}

.conditions-container {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  background: white;
}

.condition-group {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #f8f9fa;
  position: relative;
}

.condition-group:last-of-type {
  margin-bottom: 15px;
}

.add-condition-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.add-condition-btn:hover {
  background: #218838;
}

.remove-condition-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 10px;
  transition: background-color 0.2s;
}

.remove-condition-btn:hover {
  background: #c82333;
}