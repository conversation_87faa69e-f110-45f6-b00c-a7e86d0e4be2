// Système de Multi Transit - Superposition de dates de transit
class MultiTransitSystem {
  constructor() {
    this.transitDates = [];
    this.isActive = false;
    this.displayOptions = {
      showOn30: true,
      showOn360: true,
      showLabels: true,
      showAspects: false
    };
    this.initializeEventListeners();
  }

  // Initialiser les écouteurs d'événements
  initializeEventListeners() {
    // Bouton principal pour ouvrir la modal
    const multiTransitBtn = document.getElementById('multi-transit-btn');
    if (multiTransitBtn) {
      multiTransitBtn.addEventListener('click', () => this.openModal());
    }

    // Fermeture de la modal
    const closeBtn = document.getElementById('close-multi-transit');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.closeModal());
    }

    // Bouton pour ajouter une date de transit
    const addTransitBtn = document.getElementById('add-transit-date-btn');
    if (addTransitBtn) {
      addTransitBtn.addEventListener('click', () => this.addTransitDate());
    }

    // Boutons d'action
    const applyBtn = document.getElementById('apply-multi-transit-btn');
    if (applyBtn) {
      applyBtn.addEventListener('click', () => this.applyMultiTransit());
    }

    const clearBtn = document.getElementById('clear-multi-transit-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => this.clearAllTransits());
    }

    const exportBtn = document.getElementById('export-multi-transit-btn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportConfiguration());
    }

    // Options d'affichage
    this.setupDisplayOptions();

    // Initialiser la date par défaut
    this.initializeDefaultDate();
  }

  // Configurer les options d'affichage
  setupDisplayOptions() {
    const options = [
      { id: 'show-multi-on-30', key: 'showOn30' },
      { id: 'show-multi-on-360', key: 'showOn360' },
      { id: 'show-multi-labels', key: 'showLabels' },
      { id: 'show-multi-aspects', key: 'showAspects' }
    ];

    options.forEach(({ id, key }) => {
      const checkbox = document.getElementById(id);
      if (checkbox) {
        checkbox.addEventListener('change', () => {
          this.displayOptions[key] = checkbox.checked;
          if (this.isActive) {
            this.redrawWithMultiTransit();
          }
        });
      }
    });
  }

  // Initialiser la date par défaut (aujourd'hui)
  initializeDefaultDate() {
    const dateInput = document.getElementById('new-transit-date');
    if (dateInput) {
      const today = new Date();
      dateInput.value = today.toISOString().split('T')[0];
    }
  }

  // Ouvrir la modal
  openModal() {
    const modal = document.getElementById('multi-transit-modal');
    if (modal) {
      modal.style.display = 'block';
      this.refreshTransitList();
    }
  }

  // Fermer la modal
  closeModal() {
    const modal = document.getElementById('multi-transit-modal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  // Ajouter une nouvelle date de transit
  addTransitDate() {
    const dateInput = document.getElementById('new-transit-date');
    const hourInput = document.getElementById('new-transit-hour');
    const minuteInput = document.getElementById('new-transit-minute');
    const colorInput = document.getElementById('new-transit-color');
    const labelInput = document.getElementById('new-transit-label');

    if (!dateInput.value) {
      alert('Veuillez sélectionner une date');
      return;
    }

    // Générer un ID unique
    const id = Date.now().toString();

    // Créer l'objet transit
    const transitDate = {
      id: id,
      date: dateInput.value,
      hour: parseInt(hourInput.value) || 12,
      minute: parseInt(minuteInput.value) || 0,
      color: colorInput.value,
      label: labelInput.value || `Transit ${this.transitDates.length + 1}`,
      positions: null // Sera calculé lors de l'application
    };

    // Ajouter à la liste
    this.transitDates.push(transitDate);

    // Réinitialiser le formulaire
    this.resetForm();

    // Rafraîchir l'affichage
    this.refreshTransitList();

    // Message de confirmation
    this.showMessage(`Transit "${transitDate.label}" ajouté avec succès`, 'success');
  }

  // Réinitialiser le formulaire d'ajout
  resetForm() {
    document.getElementById('new-transit-label').value = '';

    // Générer une nouvelle couleur aléatoire
    const colors = ['#ff6b35', '#f7931e', '#ffd700', '#32cd32', '#00bfff', '#9370db', '#ff69b4'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    document.getElementById('new-transit-color').value = randomColor;
  }

  // Supprimer une date de transit
  removeTransitDate(id) {
    this.transitDates = this.transitDates.filter(transit => transit.id !== id);
    this.refreshTransitList();

    if (this.isActive) {
      this.redrawWithMultiTransit();
    }

    this.showMessage('Transit supprimé', 'info');
  }

  // Rafraîchir la liste des transits
  refreshTransitList() {
    const container = document.getElementById('transit-dates-list');
    if (!container) return;

    if (this.transitDates.length === 0) {
      container.innerHTML = '<div class="empty-transit-list">Aucune date de transit ajoutée</div>';
      return;
    }

    container.innerHTML = this.transitDates.map(transit => `
      <div class="transit-date-item" style="border-left-color: ${transit.color};">
        <div class="transit-date-info">
          <div class="transit-color-indicator" style="background-color: ${transit.color};"></div>
          <div class="transit-date-details">
            <div class="transit-date-main">
              ${this.formatDate(transit.date)} à ${transit.hour.toString().padStart(2, '0')}:${transit.minute.toString().padStart(2, '0')}
            </div>
            <div class="transit-date-label">${transit.label}</div>
          </div>
        </div>
        <div class="transit-date-actions">
          <button class="edit-transit-btn" onclick="multiTransitSystem.editTransitDate('${transit.id}')">Éditer</button>
          <button class="remove-transit-btn" onclick="multiTransitSystem.removeTransitDate('${transit.id}')">Supprimer</button>
        </div>
      </div>
    `).join('');
  }

  // Formater une date pour l'affichage
  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  // Éditer une date de transit
  editTransitDate(id) {
    const transit = this.transitDates.find(t => t.id === id);
    if (!transit) return;

    // Remplir le formulaire avec les valeurs existantes
    document.getElementById('new-transit-date').value = transit.date;
    document.getElementById('new-transit-hour').value = transit.hour;
    document.getElementById('new-transit-minute').value = transit.minute;
    document.getElementById('new-transit-color').value = transit.color;
    document.getElementById('new-transit-label').value = transit.label;

    // Supprimer l'ancien transit
    this.removeTransitDate(id);

    this.showMessage('Transit chargé pour modification', 'info');
  }

  // Appliquer le multi transit
  async applyMultiTransit() {
    if (this.transitDates.length === 0) {
      alert('Veuillez ajouter au moins une date de transit');
      return;
    }

    this.showMessage('Calcul des positions en cours...', 'info');
    console.log('Starting multi-transit calculation for', this.transitDates.length, 'dates');

    try {
      // Calculer les positions pour chaque date
      for (let transit of this.transitDates) {
        if (!transit.positions) {
          console.log(`Calculating positions for ${transit.label} (${transit.date})`);
          transit.positions = await this.calculatePositions(transit.date, transit.hour, transit.minute);
          console.log(`Positions calculated for ${transit.label}:`, transit.positions);
        }
      }

      // Activer le mode multi transit
      this.isActive = true;

      // Redessiner avec les multi transits
      this.redrawWithMultiTransit();

      // Fermer la modal
      this.closeModal();

      // Changer l'apparence du bouton
      this.updateButtonState(true);

      this.showMessage(`Multi Transit activé avec ${this.transitDates.length} date(s)`, 'success');
      console.log('Multi-transit system activated successfully');

    } catch (error) {
      console.error('Erreur lors du calcul des positions:', error);
      this.showMessage('Erreur lors du calcul des positions', 'error');
    }
  }

  // Calculer les positions planétaires pour une date donnée
  async calculatePositions(date, hour, minute) {
    // Utiliser l'API Swiss Ephemeris existante
    const birthData = {
      year: parseInt(date.split('-')[0]),
      month: parseInt(date.split('-')[1]),
      day: parseInt(date.split('-')[2]),
      hour: hour,
      minute: minute,
      second: 0,
      latitude: 48.8566, // Paris par défaut
      longitude: 2.3522,
      timezone: 1
    };

    try {
      const response = await fetch('https://json.astrologyapi.com/v1/planets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Basic ' + btoa('613796:60cb5a2b9b0c0e8c7b8b4c5d6e7f8a9b')
        },
        body: JSON.stringify(birthData)
      });

      if (!response.ok) {
        throw new Error('Erreur API');
      }

      const data = await response.json();
      return this.processApiResponse(data);

    } catch (error) {
      console.error('Erreur API:', error);
      // Fallback avec positions simulées
      return this.generateMockPositions();
    }
  }

  // Traiter la réponse de l'API
  processApiResponse(data) {
    const positions = {};

    if (data && data.planets) {
      data.planets.forEach(planet => {
        const planetKey = this.getPlanetKey(planet.name);
        if (planetKey) {
          positions[planetKey] = {
            sign: planet.sign,
            degree: planet.norm_degree,
            fullDegree: planet.full_degree
          };
        }
      });
    } else {
      console.log('No planets data in API response, using fallback');
      return this.generateMockPositions();
    }

    console.log('Processed positions:', positions);
    return positions;
  }

  // Obtenir la clé de planète standardisée
  getPlanetKey(planetName) {
    const mapping = {
      'Sun': 'sun',
      'Moon': 'moon',
      'Mercury': 'mercury',
      'Venus': 'venus',
      'Mars': 'mars',
      'Jupiter': 'jupiter',
      'Saturn': 'saturn',
      'Uranus': 'uranus',
      'Neptune': 'neptune',
      'Pluto': 'pluto'
    };
    return mapping[planetName] || null;
  }

  // Générer des positions simulées (fallback)
  generateMockPositions() {
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];
    const positions = {};
    const signNames = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                      'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];

    planets.forEach(planet => {
      const signIndex = Math.floor(Math.random() * 12);
      const degree = Math.random() * 30;
      const fullDegree = signIndex * 30 + degree;

      positions[planet] = {
        sign: signNames[signIndex],
        degree: degree.toFixed(2),
        fullDegree: fullDegree.toFixed(2)
      };
    });

    console.log('Generated mock positions:', positions);
    return positions;
  }

  // Redessiner avec les multi transits
  redrawWithMultiTransit() {
    // Stocker les transits multiples dans une variable globale
    window.multiTransitDates = this.transitDates;
    window.multiTransitOptions = this.displayOptions;

    console.log('Redrawing with multi-transit data:', {
      dates: this.transitDates,
      options: this.displayOptions
    });

    // Force canvas resize by clearing dimensions first
    const canvas = document.getElementById('circular-zodiac-canvas');
    if (canvas) {
      canvas.style.height = 'auto';
      // Small delay to allow DOM update
      setTimeout(() => {
        // Redessiner le zodiaque principal
        if (window.lastBirthPositions && window.lastTransitPositions) {
          if (window.isChart360Mode) {
            if (typeof window.draw360ChartOnMain === 'function') {
              window.draw360ChartOnMain();
            }
          } else {
            // Utiliser la fonction globale ou déclencher un recalcul
            if (typeof window.drawCircularProportionalZodiac === 'function') {
              window.drawCircularProportionalZodiac(window.lastBirthPositions, window.lastTransitPositions);
            } else {
              // Déclencher un recalcul du graphique
              const calculateBtn = document.getElementById('calculate-btn');
              if (calculateBtn) {
                calculateBtn.click();
              }
            }
          }
        }
      }, 50);
    }
  }

  // Dessiner les multi-transits sur le canvas (appelé depuis sidepanel-simple.js)
  static drawMultiTransitsOnCanvas(ctx, centerX, centerY, outerRadius, is360Mode = false) {
    if (!window.multiTransitDates || !window.multiTransitOptions) return;

    const transitDates = window.multiTransitDates;
    const options = window.multiTransitOptions;

    // Vérifier si on doit afficher sur ce mode
    if ((is360Mode && !options.showOn360) || (!is360Mode && !options.showOn30)) {
      return;
    }

    // Calculer les rayons pour les cercles extérieurs
    const baseRadius = outerRadius;
    const radiusIncrement = 25; // Espacement entre les cercles

    transitDates.forEach((transit, index) => {
      if (!transit.positions) return;

      const transitRadius = baseRadius + (index + 1) * radiusIncrement;
      const color = transit.color;

      // Dessiner le cercle de base pour ce transit
      ctx.beginPath();
      ctx.arc(centerX, centerY, transitRadius, 0, 2 * Math.PI);
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.setLineDash([3, 3]); // Ligne pointillée
      ctx.stroke();
      ctx.setLineDash([]); // Reset

      // Dessiner les planètes sur ce cercle
      Object.entries(transit.positions).forEach(([planet, position]) => {
        let angle;

        if (is360Mode) {
          // Mode 360° : utiliser la position complète
          angle = (position.fullDegree * Math.PI) / 180;
        } else {
          // Mode 30° : utiliser la position proportionnelle
          angle = (position.degree * Math.PI) / 180;
        }

        // Calculer la position sur le cercle
        const x = centerX + transitRadius * Math.cos(angle - Math.PI / 2);
        const y = centerY + transitRadius * Math.sin(angle - Math.PI / 2);

        // Dessiner le marqueur de planète
        MultiTransitSystem.drawPlanetMarker(ctx, x, y, planet, color, transit.label, options.showLabels);
      });

      // Dessiner le libellé du transit si activé
      if (options.showLabels && transit.label) {
        const labelRadius = transitRadius + 15;
        const labelX = centerX + labelRadius * Math.cos(-Math.PI / 2);
        const labelY = centerY + labelRadius * Math.sin(-Math.PI / 2);

        ctx.fillStyle = color;
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(transit.label, labelX, labelY);
      }
    });
  }

  // Dessiner un marqueur de planète pour les multi-transits
  static drawPlanetMarker(ctx, x, y, planet, color, transitLabel, showLabels) {
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆',
      'pluto': '♇'
    };

    const symbol = planetSymbols[planet] || '●';

    // Dessiner le fond du marqueur
    ctx.beginPath();
    ctx.arc(x, y, 8, 0, 2 * Math.PI);
    ctx.fillStyle = color;
    ctx.globalAlpha = 0.8;
    ctx.fill();
    ctx.globalAlpha = 1.0;

    // Dessiner la bordure
    ctx.beginPath();
    ctx.arc(x, y, 8, 0, 2 * Math.PI);
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Dessiner le symbole de la planète
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 10px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(symbol, x, y);

    // Dessiner le nom de la planète si les libellés sont activés
    if (showLabels) {
      ctx.fillStyle = color;
      ctx.font = '8px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      const planetNames = {
        'sun': 'Sol',
        'moon': 'Lun',
        'mercury': 'Mer',
        'venus': 'Ven',
        'mars': 'Mar',
        'jupiter': 'Jup',
        'saturn': 'Sat',
        'uranus': 'Ura',
        'neptune': 'Nep',
        'pluto': 'Plu'
      };
      const planetName = planetNames[planet] || planet;
      ctx.fillText(planetName, x, y + 10);
    }
  }

  // Dessiner les aspects entre transits (si activé)
  static drawMultiTransitAspects(ctx, centerX, centerY, outerRadius) {
    if (!window.multiTransitDates || !window.multiTransitOptions || !window.multiTransitOptions.showAspects) {
      return;
    }

    const transitDates = window.multiTransitDates;
    const radiusIncrement = 25;

    // Comparer chaque transit avec les autres
    for (let i = 0; i < transitDates.length; i++) {
      for (let j = i + 1; j < transitDates.length; j++) {
        const transit1 = transitDates[i];
        const transit2 = transitDates[j];

        if (!transit1.positions || !transit2.positions) continue;

        const radius1 = outerRadius + (i + 1) * radiusIncrement;
        const radius2 = outerRadius + (j + 1) * radiusIncrement;

        // Chercher les aspects entre les planètes des deux transits
        Object.entries(transit1.positions).forEach(([planet1, pos1]) => {
          Object.entries(transit2.positions).forEach(([planet2, pos2]) => {
            const aspectInfo = MultiTransitSystem.calculateAspectBetweenPositions(pos1, pos2);

            if (aspectInfo) {
              // Dessiner la ligne d'aspect
              const angle1 = (pos1.fullDegree * Math.PI) / 180;
              const angle2 = (pos2.fullDegree * Math.PI) / 180;

              const x1 = centerX + radius1 * Math.cos(angle1 - Math.PI / 2);
              const y1 = centerY + radius1 * Math.sin(angle1 - Math.PI / 2);
              const x2 = centerX + radius2 * Math.cos(angle2 - Math.PI / 2);
              const y2 = centerY + radius2 * Math.sin(angle2 - Math.PI / 2);

              ctx.beginPath();
              ctx.moveTo(x1, y1);
              ctx.lineTo(x2, y2);
              ctx.strokeStyle = aspectInfo.color;
              ctx.lineWidth = 1;
              ctx.setLineDash([2, 2]);
              ctx.stroke();
              ctx.setLineDash([]);
            }
          });
        });
      }
    }
  }

  // Calculer un aspect entre deux positions
  static calculateAspectBetweenPositions(pos1, pos2) {
    const diff = Math.abs(pos1.fullDegree - pos2.fullDegree);
    const normalizedDiff = Math.min(diff, 360 - diff);

    const aspects = [
      { name: 'Conjonction', angle: 0, orb: 2, color: '#FF0000' },
      { name: 'Sextile', angle: 60, orb: 2, color: '#00AA00' },
      { name: 'Carré', angle: 90, orb: 2, color: '#FF6600' },
      { name: 'Trigone', angle: 120, orb: 1, color: '#0066FF' },
      { name: 'Opposition', angle: 180, orb: 2, color: '#000000' }
    ];

    for (const aspect of aspects) {
      if (Math.abs(normalizedDiff - aspect.angle) <= aspect.orb) {
        return {
          name: aspect.name,
          angle: aspect.angle,
          orb: Math.abs(normalizedDiff - aspect.angle),
          color: aspect.color
        };
      }
    }

    return null;
  }

  // Effacer tous les transits
  clearAllTransits() {
    if (this.transitDates.length === 0) {
      this.showMessage('Aucun transit à effacer', 'info');
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir effacer tous les transits ?')) {
      this.transitDates = [];
      this.isActive = false;

      // Nettoyer les variables globales
      window.multiTransitDates = [];
      window.multiTransitOptions = null;

      // Force canvas resize back to normal
      const canvas = document.getElementById('circular-zodiac-canvas');
      if (canvas) {
        canvas.style.height = 'auto';
        setTimeout(() => {
          // Redessiner sans multi transit
          this.redrawWithoutMultiTransit();
        }, 50);
      } else {
        this.redrawWithoutMultiTransit();
      }

      // Remettre le bouton à l'état normal
      this.updateButtonState(false);

      // Rafraîchir l'affichage
      this.refreshTransitList();

      this.showMessage('Tous les transits ont été effacés', 'success');
    }
  }

  // Désactiver le multi transit
  deactivateMultiTransit() {
    this.isActive = false;
    this.transitDates = [];

    // Clear global multi-transit data
    window.multiTransitDates = [];
    window.multiTransitOptions = null;

    // Changer l'apparence du bouton
    this.updateButtonState(false);

    // Force canvas resize back to normal
    const canvas = document.getElementById('circular-zodiac-canvas');
    if (canvas) {
      canvas.style.height = 'auto';
      setTimeout(() => {
        // Redessiner sans les multi transits
        this.redrawWithoutMultiTransit();
      }, 50);
    } else {
      this.redrawWithoutMultiTransit();
    }

    this.showMessage('Multi Transit désactivé', 'info');
  }

  // Redessiner sans multi transit
  redrawWithoutMultiTransit() {
    if (window.lastBirthPositions && window.lastTransitPositions) {
      if (window.isChart360Mode) {
        if (typeof window.draw360ChartOnMain === 'function') {
          window.draw360ChartOnMain();
        }
      } else {
        if (typeof window.drawCircularProportionalZodiac === 'function') {
          window.drawCircularProportionalZodiac(window.lastBirthPositions, window.lastTransitPositions);
        } else {
          // Déclencher un recalcul du graphique
          const calculateBtn = document.getElementById('calculate-btn');
          if (calculateBtn) {
            calculateBtn.click();
          }
        }
      }
    }
  }

  // Exporter la configuration
  exportConfiguration() {
    if (this.transitDates.length === 0) {
      alert('Aucune configuration à exporter');
      return;
    }

    const config = {
      transitDates: this.transitDates,
      displayOptions: this.displayOptions,
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `multi-transit-config-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.showMessage('Configuration exportée', 'success');
  }

  // Mettre à jour l'état du bouton
  updateButtonState(isActive) {
    const button = document.getElementById('multi-transit-btn');
    if (button) {
      if (isActive) {
        button.textContent = 'Multi Transit (Actif)';
        button.style.backgroundColor = '#dc3545';
        // Change click handler to deactivate when active
        button.onclick = () => this.deactivateMultiTransit();
      } else {
        button.textContent = 'Multi Transit';
        button.style.backgroundColor = '#6f42c1';
        // Restore original click handler
        button.onclick = () => this.openModal();
      }
    }
  }

  // Afficher un message
  showMessage(message, type = 'info') {
    // Utiliser le système de messages existant si disponible
    if (window.astrologicalRulesSystem && window.astrologicalRulesSystem.showMessage) {
      window.astrologicalRulesSystem.showMessage(message, type);
    } else {
      console.log(`[Multi Transit] ${message}`);
    }
  }
}

// Initialiser le système Multi Transit
let multiTransitSystem;

document.addEventListener('DOMContentLoaded', function() {
  multiTransitSystem = new MultiTransitSystem();
});
