// Système de gestion des multi-transits
class MultiTransitSystem {
  constructor() {
    this.transits = [];
    this.isActive = false;
    this.options = {
      showCurrentTransit: true,
      autoResize: true,
      showLabels: false
    };
    this.initializeEventListeners();
  }

  // Initialiser les écouteurs d'événements
  initializeEventListeners() {
    // Bouton principal pour ouvrir la modal
    const multiTransitBtn = document.getElementById('multi-transit-btn');
    if (multiTransitBtn) {
      multiTransitBtn.addEventListener('click', () => this.openModal());
    }

    // Fermeture de la modal
    const closeBtn = document.getElementById('close-multi-transit');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.closeModal());
    }

    // Bouton d'ajout de transit
    const addTransitBtn = document.getElementById('add-transit-btn');
    if (addTransitBtn) {
      addTransitBtn.addEventListener('click', () => this.addTransit());
    }

    // Bouton d'effacement de tous les transits
    const clearAllBtn = document.getElementById('clear-all-transits-btn');
    if (clearAllBtn) {
      clearAllBtn.addEventListener('click', () => this.clearAllTransits());
    }

    // Options d'affichage
    const showCurrentTransitCheckbox = document.getElementById('show-current-transit');
    if (showCurrentTransitCheckbox) {
      showCurrentTransitCheckbox.addEventListener('change', (e) => {
        this.options.showCurrentTransit = e.target.checked;
        this.updateDisplay();
      });
    }

    const autoResizeCheckbox = document.getElementById('auto-resize-transits');
    if (autoResizeCheckbox) {
      autoResizeCheckbox.addEventListener('change', (e) => {
        this.options.autoResize = e.target.checked;
        this.updateDisplay();
      });
    }

    const showLabelsCheckbox = document.getElementById('show-transit-labels');
    if (showLabelsCheckbox) {
      showLabelsCheckbox.addEventListener('change', (e) => {
        this.options.showLabels = e.target.checked;
        this.updateDisplay();
      });
    }

    // Fermeture de la modal en cliquant à l'extérieur
    const modal = document.getElementById('multi-transit-modal');
    if (modal) {
      modal.addEventListener('click', (event) => {
        if (event.target === modal) {
          this.closeModal();
        }
      });
    }

    // Initialiser la date par défaut
    this.initializeDefaultDate();
  }

  // Initialiser la date par défaut à aujourd'hui
  initializeDefaultDate() {
    const dateInput = document.getElementById('transit-add-date');
    if (dateInput) {
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      dateInput.value = formattedDate;
    }
  }

  // Ouvrir la modal
  openModal() {
    const modal = document.getElementById('multi-transit-modal');
    if (modal) {
      modal.style.display = 'block';
      this.refreshTransitsList();
    }
  }

  // Fermer la modal
  closeModal() {
    const modal = document.getElementById('multi-transit-modal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  // Ajouter un nouveau transit
  addTransit() {
    const name = document.getElementById('transit-name').value.trim();
    const color = document.getElementById('transit-color').value;
    const date = document.getElementById('transit-add-date').value;
    const hour = parseInt(document.getElementById('transit-add-hour').value);
    const minute = parseInt(document.getElementById('transit-add-minute').value);

    // Validation
    if (!name) {
      alert('Veuillez entrer un nom pour le transit.');
      return;
    }

    if (!date) {
      alert('Veuillez sélectionner une date.');
      return;
    }

    // Créer l'objet transit
    const transit = {
      id: Date.now(), // ID unique basé sur le timestamp
      name: name,
      color: color,
      date: date,
      hour: hour,
      minute: minute,
      datetime: new Date(date + 'T' + String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0') + ':00')
    };

    // Ajouter à la liste
    this.transits.push(transit);

    // Sauvegarder dans le localStorage
    this.saveToStorage();

    // Rafraîchir l'affichage
    this.refreshTransitsList();

    // Réinitialiser le formulaire
    this.resetForm();

    // Mettre à jour l'affichage du zodiaque
    this.updateDisplay();

    console.log('Transit ajouté:', transit);
  }

  // Supprimer un transit
  removeTransit(transitId) {
    this.transits = this.transits.filter(t => t.id !== transitId);
    this.saveToStorage();
    this.refreshTransitsList();
    this.updateDisplay();
  }

  // Effacer tous les transits
  clearAllTransits() {
    if (this.transits.length === 0) {
      alert('Aucun transit à effacer.');
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir effacer tous les transits ?')) {
      this.transits = [];
      this.saveToStorage();
      this.refreshTransitsList();
      this.updateDisplay();
    }
  }

  // Rafraîchir la liste des transits dans la modal
  refreshTransitsList() {
    const container = document.getElementById('active-transits-list');
    if (!container) return;

    if (this.transits.length === 0) {
      container.innerHTML = '<div class="empty-transits-message">Aucun transit ajouté</div>';
      return;
    }

    // Trier les transits par date
    const sortedTransits = [...this.transits].sort((a, b) => a.datetime - b.datetime);

    container.innerHTML = sortedTransits.map(transit => `
      <div class="transit-item" data-transit-id="${transit.id}">
        <div class="transit-info">
          <div class="transit-color-indicator" style="background-color: ${transit.color};"></div>
          <div class="transit-details">
            <div class="transit-name">${transit.name}</div>
            <div class="transit-date">${this.formatTransitDate(transit)}</div>
          </div>
        </div>
        <div class="transit-actions">
          <button class="transit-action-btn edit-btn" onclick="multiTransitSystem.editTransit(${transit.id})">
            Éditer
          </button>
          <button class="transit-action-btn delete-btn" onclick="multiTransitSystem.removeTransit(${transit.id})">
            Supprimer
          </button>
        </div>
      </div>
    `).join('');
  }

  // Formater la date d'affichage d'un transit
  formatTransitDate(transit) {
    const date = new Date(transit.datetime);
    const options = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return date.toLocaleDateString('fr-FR', options);
  }

  // Éditer un transit (pour l'instant, juste supprimer et recréer)
  editTransit(transitId) {
    const transit = this.transits.find(t => t.id === transitId);
    if (!transit) return;

    // Remplir le formulaire avec les valeurs existantes
    document.getElementById('transit-name').value = transit.name;
    document.getElementById('transit-color').value = transit.color;
    document.getElementById('transit-add-date').value = transit.date;
    document.getElementById('transit-add-hour').value = transit.hour;
    document.getElementById('transit-add-minute').value = transit.minute;

    // Supprimer l'ancien transit
    this.removeTransit(transitId);

    // Faire défiler vers le formulaire
    document.querySelector('.add-transit-section').scrollIntoView({ behavior: 'smooth' });
  }

  // Réinitialiser le formulaire
  resetForm() {
    document.getElementById('transit-name').value = '';
    document.getElementById('transit-color').value = '#ff6b35';
    document.getElementById('transit-add-hour').value = '12';
    document.getElementById('transit-add-minute').value = '0';
    
    // Garder la date actuelle
    this.initializeDefaultDate();
  }

  // Sauvegarder dans le localStorage
  saveToStorage() {
    try {
      localStorage.setItem('multiTransits', JSON.stringify(this.transits));
      localStorage.setItem('multiTransitOptions', JSON.stringify(this.options));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des multi-transits:', error);
    }
  }

  // Charger depuis le localStorage
  loadFromStorage() {
    try {
      const savedTransits = localStorage.getItem('multiTransits');
      if (savedTransits) {
        this.transits = JSON.parse(savedTransits);
        // Reconvertir les dates
        this.transits.forEach(transit => {
          transit.datetime = new Date(transit.datetime);
        });
      }

      const savedOptions = localStorage.getItem('multiTransitOptions');
      if (savedOptions) {
        this.options = { ...this.options, ...JSON.parse(savedOptions) };
        this.updateOptionsUI();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des multi-transits:', error);
      this.transits = [];
    }
  }

  // Mettre à jour l'interface des options
  updateOptionsUI() {
    const showCurrentTransitCheckbox = document.getElementById('show-current-transit');
    if (showCurrentTransitCheckbox) {
      showCurrentTransitCheckbox.checked = this.options.showCurrentTransit;
    }

    const autoResizeCheckbox = document.getElementById('auto-resize-transits');
    if (autoResizeCheckbox) {
      autoResizeCheckbox.checked = this.options.autoResize;
    }

    const showLabelsCheckbox = document.getElementById('show-transit-labels');
    if (showLabelsCheckbox) {
      showLabelsCheckbox.checked = this.options.showLabels;
    }
  }

  // Mettre à jour l'affichage du zodiaque
  updateDisplay() {
    // Stocker les transits dans une variable globale pour le système de dessin
    window.multiTransits = this.transits;
    window.multiTransitOptions = this.options;

    // Redessiner le zodiaque si les positions sont disponibles
    if (window.birthPositions && window.transitPositions && window.drawCircularProportionalZodiac) {
      window.drawCircularProportionalZodiac(window.birthPositions, window.transitPositions);
    }
  }

  // Activer/désactiver le mode multi-transit
  toggleActive() {
    this.isActive = !this.isActive;
    this.updateDisplay();
  }

  // Obtenir les transits actifs
  getActiveTransits() {
    return this.transits;
  }
}

// Initialiser le système de multi-transits
let multiTransitSystem;

// Initialiser quand le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
  multiTransitSystem = new MultiTransitSystem();
  multiTransitSystem.loadFromStorage();
});
