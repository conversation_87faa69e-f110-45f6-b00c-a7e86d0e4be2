// Système de zodiaque linéaire 30° pour multi-transit
class LinearZodiacSystem {
  constructor() {
    this.modal = null;
    this.canvas = null;
    this.ctx = null;
    this.currentBirthPositions = null;
    this.currentTransitPositions = null;
    this.multiTransits = [];
    this.isVisible = false;

    this.init();
  }

  init() {
    // Initialiser les éléments DOM
    this.section = document.getElementById('linear-zodiac-section');
    this.canvas = document.getElementById('linear-zodiac-canvas');

    if (this.canvas) {
      this.ctx = this.canvas.getContext('2d');
    }

    // Ajouter les event listeners
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Bouton d'ouverture
    const openBtn = document.getElementById('show-linear-zodiac-btn');
    if (openBtn) {
      openBtn.addEventListener('click', () => this.show());
    }

    // Bouton de fermeture
    const closeBtn = document.getElementById('linear-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    // Bouton d'actualisation
    const refreshBtn = document.getElementById('linear-refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refresh());
    }

    // Bouton de téléchargement
    const downloadBtn = document.getElementById('linear-download-btn');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => this.download());
    }

    // Multi-transit controls
    this.setupMultiTransitListeners();
  }

  setupMultiTransitListeners() {
    // Toggle formulaire multi-transit
    const toggleBtn = document.getElementById('toggle-multi-transit-form');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleMultiTransitForm());
    }

    // Ajouter transit
    const addBtn = document.getElementById('add-multi-transit');
    if (addBtn) {
      addBtn.addEventListener('click', () => this.addMultiTransit());
    }

    // Annuler ajout
    const cancelBtn = document.getElementById('cancel-multi-transit');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.cancelMultiTransitForm());
    }

    // Initialiser la date par défaut
    const dateInput = document.getElementById('multi-transit-date');
    if (dateInput && !dateInput.value) {
      const today = new Date();
      dateInput.value = today.toISOString().split('T')[0];
    }
  }

  toggleMultiTransitForm() {
    const form = document.getElementById('multi-transit-form');
    const toggleBtn = document.getElementById('toggle-multi-transit-form');

    if (form.style.display === 'none') {
      form.style.display = 'block';
      toggleBtn.textContent = '✕ Annuler';
      toggleBtn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
    } else {
      this.cancelMultiTransitForm();
    }
  }

  cancelMultiTransitForm() {
    const form = document.getElementById('multi-transit-form');
    const toggleBtn = document.getElementById('toggle-multi-transit-form');

    form.style.display = 'none';
    toggleBtn.textContent = '+ Ajouter Transit';
    toggleBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

    // Reset form
    document.getElementById('multi-transit-date').value = new Date().toISOString().split('T')[0];
    document.getElementById('multi-transit-hour').value = '12';
    document.getElementById('multi-transit-minute').value = '0';
  }

  async addMultiTransit() {
    const dateInput = document.getElementById('multi-transit-date');
    const hourInput = document.getElementById('multi-transit-hour');
    const minuteInput = document.getElementById('multi-transit-minute');

    const date = dateInput.value;
    const hour = parseInt(hourInput.value);
    const minute = parseInt(minuteInput.value);

    if (!date) {
      alert('Veuillez sélectionner une date');
      return;
    }

    // Créer la date complète
    const transitDate = new Date(date);
    transitDate.setHours(hour, minute, 0, 0);

    try {
      // Calculer les positions planétaires pour cette date
      const positions = await this.calculateTransitPositions(transitDate);

      // Ajouter le transit à la liste
      const transit = {
        date: transitDate.toISOString(),
        positions: positions,
        id: Date.now() // ID unique
      };

      if (!this.multiTransits) {
        this.multiTransits = [];
      }

      this.multiTransits.push(transit);

      // Mettre à jour l'affichage
      this.updateMultiTransitList();
      this.cancelMultiTransitForm();

      // Redessiner le zodiaque
      this.draw();

    } catch (error) {
      console.error('Erreur lors du calcul des positions:', error);
      alert('Erreur lors du calcul des positions planétaires');
    }
  }

  async calculateTransitPositions(date) {
    // Utiliser la même fonction que le système principal
    if (typeof calculatePlanetaryPositions === 'function') {
      return await calculatePlanetaryPositions(date);
    } else {
      // Fallback si la fonction n'est pas disponible
      console.warn('calculatePlanetaryPositions not available, using mock data');
      return this.getMockPositions();
    }
  }

  getMockPositions() {
    // Positions fictives pour test
    return {
      sun: { sign: 'Aries', degree: 15.5 },
      moon: { sign: 'Taurus', degree: 8.2 },
      mercury: { sign: 'Pisces', degree: 22.1 },
      venus: { sign: 'Gemini', degree: 3.7 },
      mars: { sign: 'Leo', degree: 18.9 },
      jupiter: { sign: 'Virgo', degree: 12.4 },
      saturn: { sign: 'Capricorn', degree: 25.8 },
      uranus: { sign: 'Aquarius', degree: 6.3 },
      neptune: { sign: 'Scorpio', degree: 14.7 }
    };
  }

  updateMultiTransitList() {
    const listContainer = document.getElementById('multi-transit-list');
    if (!listContainer) return;

    if (!this.multiTransits || this.multiTransits.length === 0) {
      listContainer.innerHTML = '<div class="multi-transit-empty">Aucun transit ajouté</div>';
      return;
    }

    const colors = ['#FF6B35', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];

    listContainer.innerHTML = this.multiTransits.map((transit, index) => {
      const date = new Date(transit.date);
      const color = colors[index % colors.length];

      return `
        <div class="multi-transit-item" data-transit-id="${transit.id}">
          <div class="multi-transit-info">
            <div class="multi-transit-color" style="background-color: ${color}"></div>
            <div class="multi-transit-date">${date.toLocaleDateString('fr-FR')}</div>
            <div class="multi-transit-time">${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}</div>
          </div>
          <button class="multi-transit-remove" data-transit-id="${transit.id}">
            Supprimer
          </button>
        </div>
      `;
    }).join('');

    // Ajouter les event listeners pour les boutons supprimer
    const removeButtons = listContainer.querySelectorAll('.multi-transit-remove');
    removeButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const transitId = parseInt(e.target.getAttribute('data-transit-id'));
        this.removeMultiTransit(transitId);
      });
    });
  }

  removeMultiTransit(transitId) {
    if (!this.multiTransits) return;

    this.multiTransits = this.multiTransits.filter(transit => transit.id !== transitId);
    this.updateMultiTransitList();
    this.draw();
  }

  clearAllMultiTransits() {
    this.multiTransits = [];
    this.updateMultiTransitList();
    this.draw();
  }

  show() {
    if (!this.section) return;

    // Récupérer les données actuelles
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Ajouter les transits par défaut si la liste est vide
    if (this.multiTransits.length === 0) {
      this.addDefaultTransits();
    }

    // Debug : vérifier les données (supprimé pour éviter les problèmes d'affichage)
    // console.log('Linear Zodiac - Birth positions:', this.currentBirthPositions);
    // console.log('Linear Zodiac - Transit positions:', this.currentTransitPositions);

    // Mettre à jour les informations de date
    this.updateDateTimeInfo();

    // Mettre à jour la liste des multi-transits
    this.updateMultiTransitList();

    // Afficher la section
    this.section.style.display = 'block';
    this.isVisible = true;

    // Dessiner le zodiaque linéaire
    setTimeout(() => {
      this.draw();
    }, 100);
  }

  async addDefaultTransits() {
    // Transit 1 : 15/03/2022
    const date1 = new Date('2022-03-15T12:00:00');
    const positions1 = await this.calculateTransitPositions(date1);

    // Transit 2 : 15/03/2025
    const date2 = new Date('2025-03-15T12:00:00');
    const positions2 = await this.calculateTransitPositions(date2);

    this.multiTransits = [
      {
        date: date1.toISOString(),
        positions: positions1,
        id: Date.now()
      },
      {
        date: date2.toISOString(),
        positions: positions2,
        id: Date.now() + 1
      }
    ];

    // Sauvegarder dans window pour persistance
    window.multiTransits = this.multiTransits;
  }

  hide() {
    if (!this.section) return;
    this.section.style.display = 'none';
    this.isVisible = false;
  }

  updateDateTimeInfo() {
    const birthInfo = document.getElementById('linear-birth-info');
    const transitInfo = document.getElementById('linear-transit-info');

    if (birthInfo) {
      const birthDate = document.getElementById('birth-date')?.value || '17/12/1991';
      const birthHour = document.getElementById('birth-hour')?.value || '7';
      const birthMinute = document.getElementById('birth-minute')?.value || '27';
      birthInfo.textContent = `Naissance: ${birthDate} ${birthHour}:${birthMinute.padStart(2, '0')}`;
    }

    if (transitInfo) {
      const transitDate = document.getElementById('transit-date')?.value || new Date().toISOString().split('T')[0];
      const transitHour = document.getElementById('transit-hour')?.value || '12';
      const transitMinute = document.getElementById('transit-minute')?.value || '0';
      transitInfo.textContent = `Transit: ${transitDate} ${transitHour}:${transitMinute.padStart(2, '0')}`;
    }
  }

  refresh() {
    // Récupérer les nouvelles données
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Mettre à jour les informations
    this.updateDateTimeInfo();

    // Redessiner
    this.draw();
  }

  download() {
    if (!this.canvas) return;

    // Créer un lien de téléchargement
    const link = document.createElement('a');
    link.download = `zodiaque-lineaire-30deg-${new Date().toISOString().split('T')[0]}.png`;
    link.href = this.canvas.toDataURL();
    link.click();
  }

  draw() {
    if (!this.ctx || !this.currentBirthPositions || !this.currentTransitPositions) return;

    // Effacer le canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Dessiner le zodiaque linéaire
    this.drawLinearZodiac();
  }

  drawLinearZodiac() {
    const width = this.canvas.width;
    const height = this.canvas.height;
    const margin = 50;
    const zodiacWidth = width - (margin * 2);
    const zodiacHeight = 120;

    // Ajuster la position Y selon le nombre de multi-transits - plus d'espace pour les marqueurs plus gros
    const multiTransitCount = this.multiTransits ? this.multiTransits.length : 0;
    const multiTransitSpace = multiTransitCount * 40 + (multiTransitCount > 0 ? 60 : 0); // Plus d'espace
    const totalHeight = zodiacHeight + multiTransitSpace + 100; // Plus d'espace total
    const startY = Math.max(multiTransitSpace + 60, (height - totalHeight) / 2 + multiTransitSpace + 40);

    // Dessiner le fond du zodiaque
    this.drawZodiacBackground(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les divisions des secteurs
    this.drawZodiacSectors(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les planètes natales (ligne du bas) - ajusté pour les marqueurs plus gros
    this.drawPlanets(this.currentBirthPositions, margin, startY + zodiacHeight - 50, zodiacWidth, false);

    // Dessiner les planètes de transit (ligne du haut) - ajusté pour les marqueurs plus gros
    this.drawPlanets(this.currentTransitPositions, margin, startY - 10, zodiacWidth, true);

    // Dessiner les multi-transits si présents
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawMultiTransits(margin, startY, zodiacWidth, zodiacHeight);
    }

    // NE PLUS dessiner les aspects - supprimé selon demande utilisateur
    // this.drawAspects(margin, startY, zodiacWidth, zodiacHeight);
  }

  drawZodiacBackground(x, y, width, height) {
    // Fond principal
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(x, y, width, height);

    // Bordure
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);
  }

  drawZodiacSectors(x, y, width, height) {
    const sectors = [
      { number: 1, start: 0, end: 2.5 },
      { number: 2, start: 2.5, end: 5 },
      { number: 3, start: 5, end: 7.5 },
      { number: 4, start: 7.5, end: 10 },
      { number: 5, start: 10, end: 12.5 },
      { number: 6, start: 12.5, end: 15 },
      { number: 7, start: 15, end: 17.5 },
      { number: 8, start: 17.5, end: 20 },
      { number: 9, start: 20, end: 22.5 },
      { number: 10, start: 22.5, end: 25 },
      { number: 11, start: 25, end: 27.5 },
      { number: 12, start: 27.5, end: 30 }
    ];

    sectors.forEach((sector, index) => {
      const sectorX = x + (sector.start / 30) * width;
      const sectorWidth = ((sector.end - sector.start) / 30) * width;

      // Couleur alternée pour les secteurs
      this.ctx.fillStyle = index % 2 === 0 ? '#ffffff' : '#f0f0f0';
      this.ctx.fillRect(sectorX, y, sectorWidth, height);

      // Bordure du secteur
      this.ctx.strokeStyle = '#dee2e6';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(sectorX, y, sectorWidth, height);

      // Numéro du secteur
      this.ctx.fillStyle = '#6f42c1';
      this.ctx.font = 'bold 18px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(sector.number.toString(), sectorX + sectorWidth / 2, y + height / 2);

      // Nom du secteur (en petit)
      this.ctx.font = '10px Arial';
      this.ctx.fillStyle = '#495057';
      this.ctx.fillText(`Secteur ${sector.number}`, sectorX + sectorWidth / 2, y + height / 2 + 20);

      // Degrés
      this.ctx.font = '8px Arial';
      this.ctx.fillStyle = '#6c757d';
      this.ctx.fillText(`${sector.start}°`, sectorX + 2, y + 12);
      this.ctx.fillText(`${sector.end}°`, sectorX + sectorWidth - 15, y + 12);
    });
  }

  drawPlanets(positions, x, y, width, isTransit) {
    if (!positions) return;

    // Debug : afficher les données reçues (supprimé pour affichage propre)
    // console.log(`Drawing ${isTransit ? 'TRANSIT' : 'NATAL'} planets:`, positions);

    // Définir les symboles des planètes
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆'
    };

    const planetNames = {
      'sun': 'Soleil',
      'moon': 'Lune',
      'mercury': 'Mercure',
      'venus': 'Vénus',
      'mars': 'Mars',
      'jupiter': 'Jupiter',
      'saturn': 'Saturne',
      'uranus': 'Uranus',
      'neptune': 'Neptune'
    };

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position) {
        // Calculer la position proportionnelle (0-30°)
        let proportionalPosition;

        if (typeof position.proportionalPosition === 'number') {
          // Si déjà calculée
          proportionalPosition = position.proportionalPosition;
        } else if (position.degree !== undefined) {
          // Calculer à partir des données existantes
          proportionalPosition = this.getProportionalPosition(position);
        } else {
          return; // Pas de données valides
        }

        // Debug : afficher la conversion (supprimé pour affichage propre)
        // console.log(`${planetKey} (${isTransit ? 'TRANSIT' : 'NATAL'}):`, {
        //   original: position,
        //   proportionalPosition: proportionalPosition
        // });

        const planetX = x + (proportionalPosition / 30) * width;

        // Créer un objet planète enrichi
        const enrichedPosition = {
          ...position,
          proportionalPosition: proportionalPosition,
          symbol: planetSymbols[planetKey] || '?',
          name: planetNames[planetKey] || planetKey,
          key: planetKey
        };

        // Dessiner la planète
        this.drawPlanet(planetX, y, enrichedPosition, isTransit);
      }
    });
  }

  // Fonction pour calculer la position proportionnelle (0-30°)
  getProportionalPosition(planetData) {
    if (!planetData) return 0;

    // console.log('Converting planet data:', planetData);

    // Si c'est déjà un nombre, le retourner
    if (typeof planetData === 'number') {
      const result = planetData % 30;
      // console.log(`Number conversion: ${planetData} → ${result}`);
      return result;
    }

    // Vérifier tous les champs possibles
    // console.log('Planet data fields:', Object.keys(planetData));

    // Si on a sign et degree
    if (planetData.sign && planetData.degree !== undefined) {
      const zodiacSigns = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                          'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];

      // Chercher l'index du signe
      let signIndex = zodiacSigns.indexOf(planetData.sign);

      // Si pas trouvé, essayer avec les noms français
      if (signIndex === -1) {
        const frenchSigns = ['Bélier', 'Taureau', 'Gémeaux', 'Cancer', 'Lion', 'Vierge',
                            'Balance', 'Scorpion', 'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'];
        signIndex = frenchSigns.indexOf(planetData.sign);
      }

      if (signIndex !== -1) {
        // Calculer la position absolue puis la convertir correctement en mode 30°
        const absolutePosition = signIndex * 30 + parseFloat(planetData.degree);
        const result = (absolutePosition / 12) % 30;
        // console.log(`Sign+Degree conversion: ${planetData.sign} ${planetData.degree}° → ${absolutePosition}° → ${result}°`);
        return result;
      }
    }

    // Chercher d'autres champs possibles
    const possibleFields = ['absolutePosition', 'position', 'longitude', 'eclipticLongitude', 'degree'];

    for (const field of possibleFields) {
      if (planetData[field] !== undefined) {
        const value = parseFloat(planetData[field]);
        if (!isNaN(value)) {
          const result = (value / 12) % 30;
          // console.log(`Field ${field} conversion: ${value}° → ${result}°`);
          return result;
        }
      }
    }

    // Si on a seulement degree
    if (planetData.degree !== undefined) {
      const degree = parseFloat(planetData.degree);
      if (!isNaN(degree)) {
        if (degree > 30) {
          // Position absolue, convertir en mode 30°
          const result = (degree / 12) % 30;
          // console.log(`Absolute degree conversion: ${degree}° → ${result}°`);
          return result;
        } else {
          // Déjà en mode 30°
          const result = degree % 30;
          // console.log(`Proportional degree: ${degree}° → ${result}°`);
          return result;
        }
      }
    }

    // console.log('No valid position found, returning 0');
    return 0;
  }

  drawPlanet(x, y, position, isTransit) {
    const radius = 15; // Plus gros pour meilleure visibilité

    // Couleurs plus contrastées selon le type
    const color = isTransit ? '#FF4500' : '#000000'; // Orange plus vif et noir pur
    const borderColor = isTransit ? '#FF6B35' : '#333333';

    // Ombre portée pour effet 3D
    this.ctx.beginPath();
    this.ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fill();

    // Cercle principal de la planète
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();

    // Bordure blanche épaisse pour contraste
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // Bordure colorée fine
    this.ctx.strokeStyle = borderColor;
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Symbole de la planète plus gros et plus net
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Nom de la planète en dessous plus visible
    this.ctx.fillStyle = color;
    this.ctx.font = 'bold 10px Arial';
    this.ctx.fillText(position.name, x, y + radius + 15);

    // Position en degrés plus lisible
    this.ctx.font = 'bold 9px Arial';
    this.ctx.fillStyle = '#495057';
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 28);
  }

  drawMultiTransits(x, y, width, height) {
    // Dessiner les transits multiples sur des lignes séparées au-dessus - plus d'espace
    this.multiTransits.forEach((transit, index) => {
      const transitY = y - 60 - (index * 40); // Plus d'espace entre les lignes

      // Ligne de fond pour ce transit avec couleur différente par transit
      const colors = ['#FF6B35', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];
      const color = colors[index % colors.length];

      this.ctx.fillStyle = `${color}20`; // Transparence 20%
      this.ctx.fillRect(x, transitY - 20, width, 40); // Plus d'espace pour les marqueurs plus gros

      // Bordure de la ligne de transit
      this.ctx.strokeStyle = color;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, transitY - 20, width, 40);

      // Label du transit avec date formatée
      this.ctx.fillStyle = color;
      this.ctx.font = 'bold 9px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'middle';
      const dateStr = this.formatTransitDate(transit.date);
      this.ctx.fillText(`T${index + 1}: ${dateStr}`, x + 5, transitY - 15); // Ajusté pour les marqueurs plus gros

      // Dessiner les planètes de ce transit
      if (transit.positions) {
        this.drawMultiTransitPlanets(transit.positions, x, transitY, width, color, index);
      }
    });
  }

  formatTransitDate(dateStr) {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    } catch (e) {
      return dateStr;
    }
  }

  drawMultiTransitPlanets(positions, x, y, width, color, transitIndex) {
    if (!positions) return;

    // Définir les symboles des planètes
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆'
    };

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position) {
        // Calculer la position proportionnelle (0-30°)
        let proportionalPosition;

        if (typeof position.proportionalPosition === 'number') {
          proportionalPosition = position.proportionalPosition;
        } else if (position.degree !== undefined) {
          proportionalPosition = this.getProportionalPosition(position);
        } else {
          return;
        }

        const planetX = x + (proportionalPosition / 30) * width;

        // Créer un objet planète enrichi
        const enrichedPosition = {
          ...position,
          proportionalPosition: proportionalPosition,
          symbol: planetSymbols[planetKey] || '?'
        };

        // Dessiner la planète avec la couleur du transit
        this.drawMultiTransitPlanet(planetX, y, enrichedPosition, color, transitIndex);
      }
    });
  }

  drawMultiTransitPlanet(x, y, position, color, transitIndex) {
    const radius = 15; // MÊME TAILLE que les marqueurs principaux

    // Ombre portée pour effet 3D (comme les marqueurs principaux)
    this.ctx.beginPath();
    this.ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fill();

    // Cercle principal de la planète avec couleur spécifique
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();

    // Bordure blanche épaisse pour contraste (comme les marqueurs principaux)
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // Bordure colorée fine
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Symbole de la planète MÊME TAILLE que les marqueurs principaux
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Position en degrés plus lisible (même style que les marqueurs principaux)
    this.ctx.font = 'bold 9px Arial';
    this.ctx.fillStyle = '#495057';
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 28);
  }

  drawAspects(x, y, width, height) {
    // Dessiner les lignes d'aspects entre planètes natales et de transit
    if (!this.currentBirthPositions || !this.currentTransitPositions) return;

    // Calculer et dessiner les aspects
    Object.entries(this.currentBirthPositions).forEach(([natalKey, natalPos]) => {
      Object.entries(this.currentTransitPositions).forEach(([transitKey, transitPos]) => {
        if (natalPos && transitPos) {
          // Calculer les positions proportionnelles
          const natalProportionalPos = typeof natalPos.proportionalPosition === 'number'
            ? natalPos.proportionalPosition
            : this.getProportionalPosition(natalPos);

          const transitProportionalPos = typeof transitPos.proportionalPosition === 'number'
            ? transitPos.proportionalPosition
            : this.getProportionalPosition(transitPos);

          const aspect = this.calculateAspect(natalProportionalPos, transitProportionalPos);
          if (aspect) {
            // Créer des objets enrichis avec les positions calculées
            const enrichedNatalPos = { ...natalPos, proportionalPosition: natalProportionalPos };
            const enrichedTransitPos = { ...transitPos, proportionalPosition: transitProportionalPos };

            this.drawAspectLine(x, y, width, height, enrichedNatalPos, enrichedTransitPos, aspect);
          }
        }
      });
    });
  }

  calculateAspect(pos1, pos2) {
    const diff = Math.abs(pos1 - pos2);
    const wrappedDiff = Math.min(diff, 30 - diff);

    // Aspects proportionnels 30° avec orb plus permissif pour test
    const aspects = [
      { name: 'Conjonction', angle: 0, orb: 1.0, color: '#ff0000' },
      { name: 'Sextile', angle: 5, orb: 1.0, color: '#00aa00' },
      { name: 'Carré', angle: 7.5, orb: 1.0, color: '#ff6600' },
      { name: 'Trigone', angle: 10, orb: 1.0, color: '#0066ff' },
      { name: 'Opposition', angle: 15, orb: 1.0, color: '#aa0000' }
    ];

    for (const aspect of aspects) {
      if (Math.abs(wrappedDiff - aspect.angle) <= aspect.orb) {
        return {
          ...aspect,
          orb: Math.abs(wrappedDiff - aspect.angle).toFixed(2)
        };
      }
    }
    return null;
  }

  drawAspectLine(x, y, width, height, natalPos, transitPos, aspect) {
    const natalX = x + (natalPos.proportionalPosition / 30) * width;
    const transitX = x + (transitPos.proportionalPosition / 30) * width;
    const natalY = y + height - 30; // Ajusté pour être plus proche des planètes natales
    const transitY = y + 20; // Ajusté pour être plus proche des planètes de transit

    // Ligne d'aspect plus visible
    this.ctx.beginPath();
    this.ctx.moveTo(natalX, natalY);
    this.ctx.lineTo(transitX, transitY);
    this.ctx.strokeStyle = aspect.color;
    this.ctx.lineWidth = 3; // Plus épais
    this.ctx.setLineDash([8, 4]); // Tirets plus visibles
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Fond blanc pour le label
    const midX = (natalX + transitX) / 2;
    const midY = (natalY + transitY) / 2;

    const labelText = `${aspect.name} (${aspect.orb}°)`;
    this.ctx.font = 'bold 9px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    // Mesurer le texte pour le fond
    const textMetrics = this.ctx.measureText(labelText);
    const textWidth = textMetrics.width + 6;
    const textHeight = 14;

    // Fond blanc
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    this.ctx.fillRect(midX - textWidth/2, midY - textHeight/2, textWidth, textHeight);

    // Bordure du fond
    this.ctx.strokeStyle = aspect.color;
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(midX - textWidth/2, midY - textHeight/2, textWidth, textHeight);

    // Texte de l'aspect
    this.ctx.fillStyle = aspect.color;
    this.ctx.fillText(labelText, midX, midY);
  }
}

// Initialiser le système de zodiaque linéaire
let linearZodiacSystem;

document.addEventListener('DOMContentLoaded', function() {
  linearZodiacSystem = new LinearZodiacSystem();

  // Synchroniser avec les changements du zodiaque principal
  const originalDrawFunction = window.drawCircularProportionalZodiac;
  if (originalDrawFunction) {
    window.drawCircularProportionalZodiac = function(birthPositions, transitPositions) {
      // Appeler la fonction originale
      const result = originalDrawFunction.call(this, birthPositions, transitPositions);

      // Mettre à jour le zodiaque linéaire s'il est visible
      if (linearZodiacSystem && linearZodiacSystem.isVisible) {
        setTimeout(() => {
          linearZodiacSystem.refresh();
        }, 100);
      }

      return result;
    };
  }
});

// Fonction globale pour ouvrir le zodiaque linéaire depuis l'extérieur
window.showLinearZodiac = function() {
  if (linearZodiacSystem) {
    linearZodiacSystem.show();
  }
};
