// Système de zodiaque linéaire 30° pour multi-transit
class LinearZodiacSystem {
  constructor() {
    this.modal = null;
    this.canvas = null;
    this.ctx = null;
    this.currentBirthPositions = null;
    this.currentTransitPositions = null;
    this.multiTransits = [];
    this.isVisible = false;

    this.init();
  }

  init() {
    // Initialiser les éléments DOM
    this.modal = document.getElementById('linear-zodiac-modal');
    this.canvas = document.getElementById('linear-zodiac-canvas');

    if (this.canvas) {
      this.ctx = this.canvas.getContext('2d');
    }

    // Ajouter les event listeners
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Bouton d'ouverture
    const openBtn = document.getElementById('show-linear-zodiac-btn');
    if (openBtn) {
      openBtn.addEventListener('click', () => this.show());
    }

    // Bouton de fermeture
    const closeBtn = document.getElementById('close-linear-zodiac');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    // Fermeture en cliquant à l'extérieur
    if (this.modal) {
      this.modal.addEventListener('click', (e) => {
        if (e.target === this.modal) {
          this.hide();
        }
      });
    }

    // Bouton d'actualisation
    const refreshBtn = document.getElementById('linear-refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refresh());
    }

    // Bouton de téléchargement
    const downloadBtn = document.getElementById('linear-download-btn');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => this.download());
    }
  }

  show() {
    if (!this.modal) return;

    // Récupérer les données actuelles
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Mettre à jour les informations de date
    this.updateDateTimeInfo();

    // Afficher la modal
    this.modal.style.display = 'block';
    this.isVisible = true;

    // Dessiner le zodiaque linéaire
    setTimeout(() => {
      this.draw();
    }, 100);
  }

  hide() {
    if (!this.modal) return;
    this.modal.style.display = 'none';
    this.isVisible = false;
  }

  updateDateTimeInfo() {
    const birthInfo = document.getElementById('linear-birth-info');
    const transitInfo = document.getElementById('linear-transit-info');

    if (birthInfo) {
      const birthDate = document.getElementById('birth-date')?.value || '17/12/1991';
      const birthHour = document.getElementById('birth-hour')?.value || '7';
      const birthMinute = document.getElementById('birth-minute')?.value || '27';
      birthInfo.textContent = `Naissance: ${birthDate} ${birthHour}:${birthMinute.padStart(2, '0')}`;
    }

    if (transitInfo) {
      const transitDate = document.getElementById('transit-date')?.value || new Date().toISOString().split('T')[0];
      const transitHour = document.getElementById('transit-hour')?.value || '12';
      const transitMinute = document.getElementById('transit-minute')?.value || '0';
      transitInfo.textContent = `Transit: ${transitDate} ${transitHour}:${transitMinute.padStart(2, '0')}`;
    }
  }

  refresh() {
    // Récupérer les nouvelles données
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Mettre à jour les informations
    this.updateDateTimeInfo();

    // Redessiner
    this.draw();
  }

  download() {
    if (!this.canvas) return;

    // Créer un lien de téléchargement
    const link = document.createElement('a');
    link.download = `zodiaque-lineaire-30deg-${new Date().toISOString().split('T')[0]}.png`;
    link.href = this.canvas.toDataURL();
    link.click();
  }

  draw() {
    if (!this.ctx || !this.currentBirthPositions || !this.currentTransitPositions) return;

    // Effacer le canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Dessiner le zodiaque linéaire
    this.drawLinearZodiac();
  }

  drawLinearZodiac() {
    const width = this.canvas.width;
    const height = this.canvas.height;
    const margin = 50;
    const zodiacWidth = width - (margin * 2);
    const zodiacHeight = 120;

    // Ajuster la position Y selon le nombre de multi-transits
    const multiTransitCount = this.multiTransits ? this.multiTransits.length : 0;
    const multiTransitSpace = multiTransitCount * 30 + (multiTransitCount > 0 ? 40 : 0);
    const totalHeight = zodiacHeight + multiTransitSpace;
    const startY = Math.max(multiTransitSpace + 20, (height - totalHeight) / 2 + multiTransitSpace);

    // Dessiner le fond du zodiaque
    this.drawZodiacBackground(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les divisions des signes
    this.drawZodiacSigns(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les planètes natales (ligne du bas)
    this.drawPlanets(this.currentBirthPositions, margin, startY + zodiacHeight - 40, zodiacWidth, false);

    // Dessiner les planètes de transit (ligne du haut)
    this.drawPlanets(this.currentTransitPositions, margin, startY + 10, zodiacWidth, true);

    // Dessiner les multi-transits si présents
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawMultiTransits(margin, startY, zodiacWidth, zodiacHeight);
    }

    // Dessiner les aspects
    this.drawAspects(margin, startY, zodiacWidth, zodiacHeight);
  }

  drawZodiacBackground(x, y, width, height) {
    // Fond principal
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(x, y, width, height);

    // Bordure
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);
  }

  drawZodiacSigns(x, y, width, height) {
    const signs = [
      { name: 'Bélier', symbol: '♈', start: 0, end: 2.5 },
      { name: 'Taureau', symbol: '♉', start: 2.5, end: 5 },
      { name: 'Gémeaux', symbol: '♊', start: 5, end: 7.5 },
      { name: 'Cancer', symbol: '♋', start: 7.5, end: 10 },
      { name: 'Lion', symbol: '♌', start: 10, end: 12.5 },
      { name: 'Vierge', symbol: '♍', start: 12.5, end: 15 },
      { name: 'Balance', symbol: '♎', start: 15, end: 17.5 },
      { name: 'Scorpion', symbol: '♏', start: 17.5, end: 20 },
      { name: 'Sagittaire', symbol: '♐', start: 20, end: 22.5 },
      { name: 'Capricorne', symbol: '♑', start: 22.5, end: 25 },
      { name: 'Verseau', symbol: '♒', start: 25, end: 27.5 },
      { name: 'Poissons', symbol: '♓', start: 27.5, end: 30 }
    ];

    signs.forEach((sign, index) => {
      const signX = x + (sign.start / 30) * width;
      const signWidth = ((sign.end - sign.start) / 30) * width;

      // Couleur alternée pour les signes
      this.ctx.fillStyle = index % 2 === 0 ? '#ffffff' : '#f0f0f0';
      this.ctx.fillRect(signX, y, signWidth, height);

      // Bordure du signe
      this.ctx.strokeStyle = '#dee2e6';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(signX, y, signWidth, height);

      // Symbole du signe
      this.ctx.fillStyle = '#495057';
      this.ctx.font = 'bold 16px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(sign.symbol, signX + signWidth / 2, y + height / 2);

      // Nom du signe (en petit)
      this.ctx.font = '10px Arial';
      this.ctx.fillText(sign.name, signX + signWidth / 2, y + height / 2 + 20);

      // Degrés
      this.ctx.font = '8px Arial';
      this.ctx.fillStyle = '#6c757d';
      this.ctx.fillText(`${sign.start}°`, signX + 2, y + 12);
      this.ctx.fillText(`${sign.end}°`, signX + signWidth - 15, y + 12);
    });
  }

  drawPlanets(positions, x, y, width, isTransit) {
    if (!positions) return;

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position && typeof position.proportionalPosition === 'number') {
        const planetX = x + (position.proportionalPosition / 30) * width;

        // Dessiner la planète
        this.drawPlanet(planetX, y, position, isTransit);
      }
    });
  }

  drawPlanet(x, y, position, isTransit) {
    const radius = 8;

    // Couleur selon le type
    const color = isTransit ? '#FF6B35' : '#333333';

    // Cercle de la planète
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Symbole de la planète
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 10px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Nom de la planète en dessous
    this.ctx.fillStyle = color;
    this.ctx.font = '8px Arial';
    this.ctx.fillText(position.name, x, y + radius + 12);

    // Position en degrés
    this.ctx.font = '7px Arial';
    this.ctx.fillStyle = '#6c757d';
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 22);
  }

  drawMultiTransits(x, y, width, height) {
    // Dessiner les transits multiples sur des lignes séparées au-dessus
    this.multiTransits.forEach((transit, index) => {
      const transitY = y - 40 - (index * 30);

      // Ligne de fond pour ce transit avec couleur différente par transit
      const colors = ['#FF6B35', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];
      const color = colors[index % colors.length];

      this.ctx.fillStyle = `${color}20`; // Transparence 20%
      this.ctx.fillRect(x, transitY - 12, width, 24);

      // Bordure de la ligne de transit
      this.ctx.strokeStyle = color;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, transitY - 12, width, 24);

      // Label du transit avec date formatée
      this.ctx.fillStyle = color;
      this.ctx.font = 'bold 9px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'middle';
      const dateStr = this.formatTransitDate(transit.date);
      this.ctx.fillText(`T${index + 1}: ${dateStr}`, x + 5, transitY - 8);

      // Dessiner les planètes de ce transit
      if (transit.positions) {
        this.drawMultiTransitPlanets(transit.positions, x, transitY, width, color, index);
      }
    });
  }

  formatTransitDate(dateStr) {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    } catch (e) {
      return dateStr;
    }
  }

  drawMultiTransitPlanets(positions, x, y, width, color, transitIndex) {
    if (!positions) return;

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position && typeof position.proportionalPosition === 'number') {
        const planetX = x + (position.proportionalPosition / 30) * width;

        // Dessiner la planète avec la couleur du transit
        this.drawMultiTransitPlanet(planetX, y, position, color, transitIndex);
      }
    });
  }

  drawMultiTransitPlanet(x, y, position, color, transitIndex) {
    const radius = 6; // Plus petit pour les multi-transits

    // Cercle de la planète avec couleur spécifique
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Symbole de la planète
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 8px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Position en degrés (plus petit)
    this.ctx.font = '6px Arial';
    this.ctx.fillStyle = color;
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 8);
  }

  drawAspects(x, y, width, height) {
    // Dessiner les lignes d'aspects entre planètes natales et de transit
    if (!this.currentBirthPositions || !this.currentTransitPositions) return;

    // Calculer et dessiner les aspects
    Object.entries(this.currentBirthPositions).forEach(([natalKey, natalPos]) => {
      Object.entries(this.currentTransitPositions).forEach(([transitKey, transitPos]) => {
        if (natalPos && transitPos &&
            typeof natalPos.proportionalPosition === 'number' &&
            typeof transitPos.proportionalPosition === 'number') {

          const aspect = this.calculateAspect(natalPos.proportionalPosition, transitPos.proportionalPosition);
          if (aspect) {
            this.drawAspectLine(x, y, width, height, natalPos, transitPos, aspect);
          }
        }
      });
    });
  }

  calculateAspect(pos1, pos2) {
    const diff = Math.abs(pos1 - pos2);
    const wrappedDiff = Math.min(diff, 30 - diff);

    // Aspects proportionnels 30°
    const aspects = [
      { name: 'Conjonction', angle: 0, orb: 0.5, color: '#ff0000' },
      { name: 'Sextile', angle: 5, orb: 0.5, color: '#00aa00' },
      { name: 'Carré', angle: 7.5, orb: 0.5, color: '#ff6600' },
      { name: 'Trigone', angle: 10, orb: 0.5, color: '#0066ff' },
      { name: 'Opposition', angle: 15, orb: 0.5, color: '#aa0000' }
    ];

    for (const aspect of aspects) {
      if (Math.abs(wrappedDiff - aspect.angle) <= aspect.orb) {
        return aspect;
      }
    }
    return null;
  }

  drawAspectLine(x, y, width, height, natalPos, transitPos, aspect) {
    const natalX = x + (natalPos.proportionalPosition / 30) * width;
    const transitX = x + (transitPos.proportionalPosition / 30) * width;
    const natalY = y + height - 40;
    const transitY = y + 10;

    // Ligne d'aspect
    this.ctx.beginPath();
    this.ctx.moveTo(natalX, natalY);
    this.ctx.lineTo(transitX, transitY);
    this.ctx.strokeStyle = aspect.color;
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([5, 5]);
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Label de l'aspect au milieu
    const midX = (natalX + transitX) / 2;
    const midY = (natalY + transitY) / 2;

    this.ctx.fillStyle = aspect.color;
    this.ctx.font = 'bold 8px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(aspect.name, midX, midY);
  }
}

// Initialiser le système de zodiaque linéaire
let linearZodiacSystem;

document.addEventListener('DOMContentLoaded', function() {
  linearZodiacSystem = new LinearZodiacSystem();

  // Synchroniser avec les changements du zodiaque principal
  const originalDrawFunction = window.drawCircularProportionalZodiac;
  if (originalDrawFunction) {
    window.drawCircularProportionalZodiac = function(birthPositions, transitPositions) {
      // Appeler la fonction originale
      const result = originalDrawFunction.call(this, birthPositions, transitPositions);

      // Mettre à jour le zodiaque linéaire s'il est visible
      if (linearZodiacSystem && linearZodiacSystem.isVisible) {
        setTimeout(() => {
          linearZodiacSystem.refresh();
        }, 100);
      }

      return result;
    };
  }
});

// Fonction globale pour ouvrir le zodiaque linéaire depuis l'extérieur
window.showLinearZodiac = function() {
  if (linearZodiacSystem) {
    linearZodiacSystem.show();
  }
};
