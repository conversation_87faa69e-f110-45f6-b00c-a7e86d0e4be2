// Système de zodiaque linéaire 30° pour multi-transit
class LinearZodiacSystem {
  constructor() {
    this.modal = null;
    this.canvas = null;
    this.ctx = null;
    this.currentBirthPositions = null;
    this.currentTransitPositions = null;
    this.multiTransits = [];
    this.isVisible = false;

    this.init();
  }

  init() {
    // Initialiser les éléments DOM
    this.section = document.getElementById('linear-zodiac-section');
    this.canvas = document.getElementById('linear-zodiac-canvas');

    if (this.canvas) {
      this.ctx = this.canvas.getContext('2d');
    }

    // Ajouter les event listeners
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Bouton d'ouverture
    const openBtn = document.getElementById('show-linear-zodiac-btn');
    if (openBtn) {
      openBtn.addEventListener('click', () => this.show());
    }

    // Bouton de fermeture
    const closeBtn = document.getElementById('linear-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    // Bouton d'actualisation
    const refreshBtn = document.getElementById('linear-refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refresh());
    }

    // Bouton de téléchargement
    const downloadBtn = document.getElementById('linear-download-btn');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => this.download());
    }
  }

  show() {
    if (!this.section) return;

    // Récupérer les données actuelles
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Debug : vérifier les données
    console.log('Linear Zodiac - Birth positions:', this.currentBirthPositions);
    console.log('Linear Zodiac - Transit positions:', this.currentTransitPositions);

    // Mettre à jour les informations de date
    this.updateDateTimeInfo();

    // Afficher la section
    this.section.style.display = 'block';
    this.isVisible = true;

    // Dessiner le zodiaque linéaire
    setTimeout(() => {
      this.draw();
    }, 100);
  }

  hide() {
    if (!this.section) return;
    this.section.style.display = 'none';
    this.isVisible = false;
  }

  updateDateTimeInfo() {
    const birthInfo = document.getElementById('linear-birth-info');
    const transitInfo = document.getElementById('linear-transit-info');

    if (birthInfo) {
      const birthDate = document.getElementById('birth-date')?.value || '17/12/1991';
      const birthHour = document.getElementById('birth-hour')?.value || '7';
      const birthMinute = document.getElementById('birth-minute')?.value || '27';
      birthInfo.textContent = `Naissance: ${birthDate} ${birthHour}:${birthMinute.padStart(2, '0')}`;
    }

    if (transitInfo) {
      const transitDate = document.getElementById('transit-date')?.value || new Date().toISOString().split('T')[0];
      const transitHour = document.getElementById('transit-hour')?.value || '12';
      const transitMinute = document.getElementById('transit-minute')?.value || '0';
      transitInfo.textContent = `Transit: ${transitDate} ${transitHour}:${transitMinute.padStart(2, '0')}`;
    }
  }

  refresh() {
    // Récupérer les nouvelles données
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Mettre à jour les informations
    this.updateDateTimeInfo();

    // Redessiner
    this.draw();
  }

  download() {
    if (!this.canvas) return;

    // Créer un lien de téléchargement
    const link = document.createElement('a');
    link.download = `zodiaque-lineaire-30deg-${new Date().toISOString().split('T')[0]}.png`;
    link.href = this.canvas.toDataURL();
    link.click();
  }

  draw() {
    if (!this.ctx || !this.currentBirthPositions || !this.currentTransitPositions) return;

    // Effacer le canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Dessiner le zodiaque linéaire
    this.drawLinearZodiac();
  }

  drawLinearZodiac() {
    const width = this.canvas.width;
    const height = this.canvas.height;
    const margin = 50;
    const zodiacWidth = width - (margin * 2);
    const zodiacHeight = 120;

    // Ajuster la position Y selon le nombre de multi-transits
    const multiTransitCount = this.multiTransits ? this.multiTransits.length : 0;
    const multiTransitSpace = multiTransitCount * 30 + (multiTransitCount > 0 ? 40 : 0);
    const totalHeight = zodiacHeight + multiTransitSpace;
    const startY = Math.max(multiTransitSpace + 20, (height - totalHeight) / 2 + multiTransitSpace);

    // Dessiner le fond du zodiaque
    this.drawZodiacBackground(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les divisions des secteurs
    this.drawZodiacSectors(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les planètes natales (ligne du bas)
    this.drawPlanets(this.currentBirthPositions, margin, startY + zodiacHeight - 40, zodiacWidth, false);

    // Dessiner les planètes de transit (ligne du haut)
    this.drawPlanets(this.currentTransitPositions, margin, startY + 10, zodiacWidth, true);

    // Dessiner les multi-transits si présents
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawMultiTransits(margin, startY, zodiacWidth, zodiacHeight);
    }

    // Dessiner les aspects
    this.drawAspects(margin, startY, zodiacWidth, zodiacHeight);
  }

  drawZodiacBackground(x, y, width, height) {
    // Fond principal
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(x, y, width, height);

    // Bordure
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);
  }

  drawZodiacSectors(x, y, width, height) {
    const sectors = [
      { number: 1, start: 0, end: 2.5 },
      { number: 2, start: 2.5, end: 5 },
      { number: 3, start: 5, end: 7.5 },
      { number: 4, start: 7.5, end: 10 },
      { number: 5, start: 10, end: 12.5 },
      { number: 6, start: 12.5, end: 15 },
      { number: 7, start: 15, end: 17.5 },
      { number: 8, start: 17.5, end: 20 },
      { number: 9, start: 20, end: 22.5 },
      { number: 10, start: 22.5, end: 25 },
      { number: 11, start: 25, end: 27.5 },
      { number: 12, start: 27.5, end: 30 }
    ];

    sectors.forEach((sector, index) => {
      const sectorX = x + (sector.start / 30) * width;
      const sectorWidth = ((sector.end - sector.start) / 30) * width;

      // Couleur alternée pour les secteurs
      this.ctx.fillStyle = index % 2 === 0 ? '#ffffff' : '#f0f0f0';
      this.ctx.fillRect(sectorX, y, sectorWidth, height);

      // Bordure du secteur
      this.ctx.strokeStyle = '#dee2e6';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(sectorX, y, sectorWidth, height);

      // Numéro du secteur
      this.ctx.fillStyle = '#6f42c1';
      this.ctx.font = 'bold 18px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(sector.number.toString(), sectorX + sectorWidth / 2, y + height / 2);

      // Nom du secteur (en petit)
      this.ctx.font = '10px Arial';
      this.ctx.fillStyle = '#495057';
      this.ctx.fillText(`Secteur ${sector.number}`, sectorX + sectorWidth / 2, y + height / 2 + 20);

      // Degrés
      this.ctx.font = '8px Arial';
      this.ctx.fillStyle = '#6c757d';
      this.ctx.fillText(`${sector.start}°`, sectorX + 2, y + 12);
      this.ctx.fillText(`${sector.end}°`, sectorX + sectorWidth - 15, y + 12);
    });
  }

  drawPlanets(positions, x, y, width, isTransit) {
    if (!positions) return;

    // Définir les symboles des planètes
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆'
    };

    const planetNames = {
      'sun': 'Soleil',
      'moon': 'Lune',
      'mercury': 'Mercure',
      'venus': 'Vénus',
      'mars': 'Mars',
      'jupiter': 'Jupiter',
      'saturn': 'Saturne',
      'uranus': 'Uranus',
      'neptune': 'Neptune'
    };

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position) {
        // Calculer la position proportionnelle (0-30°)
        let proportionalPosition;

        if (typeof position.proportionalPosition === 'number') {
          // Si déjà calculée
          proportionalPosition = position.proportionalPosition;
        } else if (position.degree !== undefined) {
          // Calculer à partir des données existantes
          proportionalPosition = this.getProportionalPosition(position);
        } else {
          return; // Pas de données valides
        }

        const planetX = x + (proportionalPosition / 30) * width;

        // Créer un objet planète enrichi
        const enrichedPosition = {
          ...position,
          proportionalPosition: proportionalPosition,
          symbol: planetSymbols[planetKey] || '?',
          name: planetNames[planetKey] || planetKey,
          key: planetKey
        };

        // Dessiner la planète
        this.drawPlanet(planetX, y, enrichedPosition, isTransit);
      }
    });
  }

  // Fonction pour calculer la position proportionnelle (0-30°)
  getProportionalPosition(planetData) {
    if (!planetData) return 0;

    // Si c'est déjà un nombre, le retourner
    if (typeof planetData === 'number') {
      return planetData % 30;
    }

    // Si on a sign et degree
    if (planetData.sign && planetData.degree !== undefined) {
      const zodiacSigns = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                          'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];

      // Chercher l'index du signe
      let signIndex = zodiacSigns.indexOf(planetData.sign);

      // Si pas trouvé, essayer avec les noms français
      if (signIndex === -1) {
        const frenchSigns = ['Bélier', 'Taureau', 'Gémeaux', 'Cancer', 'Lion', 'Vierge',
                            'Balance', 'Scorpion', 'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'];
        signIndex = frenchSigns.indexOf(planetData.sign);
      }

      if (signIndex !== -1) {
        // Calculer la position absolue puis la convertir en proportionnelle
        const absolutePosition = signIndex * 30 + parseFloat(planetData.degree);
        return (absolutePosition / 12) % 30;
      }
    }

    // Si on a seulement degree, l'utiliser directement
    if (planetData.degree !== undefined) {
      return parseFloat(planetData.degree) % 30;
    }

    return 0;
  }

  drawPlanet(x, y, position, isTransit) {
    const radius = 8;

    // Couleur selon le type
    const color = isTransit ? '#FF6B35' : '#333333';

    // Cercle de la planète
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Symbole de la planète
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 10px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Nom de la planète en dessous
    this.ctx.fillStyle = color;
    this.ctx.font = '8px Arial';
    this.ctx.fillText(position.name, x, y + radius + 12);

    // Position en degrés
    this.ctx.font = '7px Arial';
    this.ctx.fillStyle = '#6c757d';
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 22);
  }

  drawMultiTransits(x, y, width, height) {
    // Dessiner les transits multiples sur des lignes séparées au-dessus
    this.multiTransits.forEach((transit, index) => {
      const transitY = y - 40 - (index * 30);

      // Ligne de fond pour ce transit avec couleur différente par transit
      const colors = ['#FF6B35', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];
      const color = colors[index % colors.length];

      this.ctx.fillStyle = `${color}20`; // Transparence 20%
      this.ctx.fillRect(x, transitY - 12, width, 24);

      // Bordure de la ligne de transit
      this.ctx.strokeStyle = color;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, transitY - 12, width, 24);

      // Label du transit avec date formatée
      this.ctx.fillStyle = color;
      this.ctx.font = 'bold 9px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'middle';
      const dateStr = this.formatTransitDate(transit.date);
      this.ctx.fillText(`T${index + 1}: ${dateStr}`, x + 5, transitY - 8);

      // Dessiner les planètes de ce transit
      if (transit.positions) {
        this.drawMultiTransitPlanets(transit.positions, x, transitY, width, color, index);
      }
    });
  }

  formatTransitDate(dateStr) {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    } catch (e) {
      return dateStr;
    }
  }

  drawMultiTransitPlanets(positions, x, y, width, color, transitIndex) {
    if (!positions) return;

    // Définir les symboles des planètes
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆'
    };

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position) {
        // Calculer la position proportionnelle (0-30°)
        let proportionalPosition;

        if (typeof position.proportionalPosition === 'number') {
          proportionalPosition = position.proportionalPosition;
        } else if (position.degree !== undefined) {
          proportionalPosition = this.getProportionalPosition(position);
        } else {
          return;
        }

        const planetX = x + (proportionalPosition / 30) * width;

        // Créer un objet planète enrichi
        const enrichedPosition = {
          ...position,
          proportionalPosition: proportionalPosition,
          symbol: planetSymbols[planetKey] || '?'
        };

        // Dessiner la planète avec la couleur du transit
        this.drawMultiTransitPlanet(planetX, y, enrichedPosition, color, transitIndex);
      }
    });
  }

  drawMultiTransitPlanet(x, y, position, color, transitIndex) {
    const radius = 6; // Plus petit pour les multi-transits

    // Cercle de la planète avec couleur spécifique
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Symbole de la planète
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 8px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Position en degrés (plus petit)
    this.ctx.font = '6px Arial';
    this.ctx.fillStyle = color;
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 8);
  }

  drawAspects(x, y, width, height) {
    // Dessiner les lignes d'aspects entre planètes natales et de transit
    if (!this.currentBirthPositions || !this.currentTransitPositions) return;

    // Calculer et dessiner les aspects
    Object.entries(this.currentBirthPositions).forEach(([natalKey, natalPos]) => {
      Object.entries(this.currentTransitPositions).forEach(([transitKey, transitPos]) => {
        if (natalPos && transitPos) {
          // Calculer les positions proportionnelles
          const natalProportionalPos = typeof natalPos.proportionalPosition === 'number'
            ? natalPos.proportionalPosition
            : this.getProportionalPosition(natalPos);

          const transitProportionalPos = typeof transitPos.proportionalPosition === 'number'
            ? transitPos.proportionalPosition
            : this.getProportionalPosition(transitPos);

          const aspect = this.calculateAspect(natalProportionalPos, transitProportionalPos);
          if (aspect) {
            // Créer des objets enrichis avec les positions calculées
            const enrichedNatalPos = { ...natalPos, proportionalPosition: natalProportionalPos };
            const enrichedTransitPos = { ...transitPos, proportionalPosition: transitProportionalPos };

            this.drawAspectLine(x, y, width, height, enrichedNatalPos, enrichedTransitPos, aspect);
          }
        }
      });
    });
  }

  calculateAspect(pos1, pos2) {
    const diff = Math.abs(pos1 - pos2);
    const wrappedDiff = Math.min(diff, 30 - diff);

    // Aspects proportionnels 30°
    const aspects = [
      { name: 'Conjonction', angle: 0, orb: 0.5, color: '#ff0000' },
      { name: 'Sextile', angle: 5, orb: 0.5, color: '#00aa00' },
      { name: 'Carré', angle: 7.5, orb: 0.5, color: '#ff6600' },
      { name: 'Trigone', angle: 10, orb: 0.5, color: '#0066ff' },
      { name: 'Opposition', angle: 15, orb: 0.5, color: '#aa0000' }
    ];

    for (const aspect of aspects) {
      if (Math.abs(wrappedDiff - aspect.angle) <= aspect.orb) {
        return aspect;
      }
    }
    return null;
  }

  drawAspectLine(x, y, width, height, natalPos, transitPos, aspect) {
    const natalX = x + (natalPos.proportionalPosition / 30) * width;
    const transitX = x + (transitPos.proportionalPosition / 30) * width;
    const natalY = y + height - 40;
    const transitY = y + 10;

    // Ligne d'aspect
    this.ctx.beginPath();
    this.ctx.moveTo(natalX, natalY);
    this.ctx.lineTo(transitX, transitY);
    this.ctx.strokeStyle = aspect.color;
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([5, 5]);
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Label de l'aspect au milieu
    const midX = (natalX + transitX) / 2;
    const midY = (natalY + transitY) / 2;

    this.ctx.fillStyle = aspect.color;
    this.ctx.font = 'bold 8px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(aspect.name, midX, midY);
  }
}

// Initialiser le système de zodiaque linéaire
let linearZodiacSystem;

document.addEventListener('DOMContentLoaded', function() {
  linearZodiacSystem = new LinearZodiacSystem();

  // Synchroniser avec les changements du zodiaque principal
  const originalDrawFunction = window.drawCircularProportionalZodiac;
  if (originalDrawFunction) {
    window.drawCircularProportionalZodiac = function(birthPositions, transitPositions) {
      // Appeler la fonction originale
      const result = originalDrawFunction.call(this, birthPositions, transitPositions);

      // Mettre à jour le zodiaque linéaire s'il est visible
      if (linearZodiacSystem && linearZodiacSystem.isVisible) {
        setTimeout(() => {
          linearZodiacSystem.refresh();
        }, 100);
      }

      return result;
    };
  }
});

// Fonction globale pour ouvrir le zodiaque linéaire depuis l'extérieur
window.showLinearZodiac = function() {
  if (linearZodiacSystem) {
    linearZodiacSystem.show();
  }
};
