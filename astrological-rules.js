// Système de règles astrologiques dynamiques
class AstrologicalRulesSystem {
  constructor() {
    this.rules = [];
    this.activeEffects = new Map();
    this.init();
  }

  async init() {
    await this.loadRulesFromStorage();
    this.initializeEventListeners();
    this.attachRuleButtonEvents();
    this.refreshRulesList();
    this.applyAllRules();
  }

  // Initialiser les écouteurs d'événements
  initializeEventListeners() {
    // Bouton principal pour ouvrir la modal
    const rulesBtn = document.getElementById('astrological-rules-btn');
    if (rulesBtn) {
      rulesBtn.addEventListener('click', () => this.openRulesModal());
    }

    // Fermeture de la modal
    const closeBtn = document.getElementById('close-astrological-rules');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.closeRulesModal());
    }

    // Gestion des effets (affichage des options)
    this.setupEffectToggles();

    // Boutons d'action
    const addRuleBtn = document.getElementById('add-rule-btn');
    if (addRuleBtn) {
      addRuleBtn.addEventListener('click', () => this.addNewRule());
    }

    // Gestion du type de condition
    const conditionType = document.getElementById('condition-type');
    if (conditionType) {
      conditionType.addEventListener('change', () => this.toggleConditionType());
    }

    // Boutons pour ajouter des conditions
    const addPositionBtn = document.getElementById('add-position-condition');
    if (addPositionBtn) {
      addPositionBtn.addEventListener('click', () => this.addPositionCondition());
    }

    const addAspectBtn = document.getElementById('add-aspect-condition');
    if (addAspectBtn) {
      addAspectBtn.addEventListener('click', () => this.addAspectCondition());
    }

    const testRuleBtn = document.getElementById('test-rule-btn');
    if (testRuleBtn) {
      testRuleBtn.addEventListener('click', () => this.testCurrentRule());
    }

    // Gestionnaires d'événements pour les boutons de suppression des conditions
    this.setupConditionRemovalListeners();

    // Slider pour l'agrandissement des planètes
    const scaleSlider = document.getElementById('planet-scale-slider');
    if (scaleSlider) {
      scaleSlider.addEventListener('input', (e) => {
        document.getElementById('planet-scale-value').textContent = e.target.value + 'x';
      });
    }
  }

  // Configuration des toggles pour les effets
  setupEffectToggles() {
    const effectCheckboxes = [
      { id: 'effect-sector-color', optionsId: 'sector-color-options' },
      { id: 'effect-planet-scale', optionsId: 'planet-scale-options' },
      { id: 'effect-sector-highlight', optionsId: 'sector-highlight-options' },
      { id: 'effect-planet-glow', optionsId: 'planet-glow-options' }
    ];

    effectCheckboxes.forEach(({ id, optionsId }) => {
      const checkbox = document.getElementById(id);
      const options = document.getElementById(optionsId);

      if (checkbox && options) {
        checkbox.addEventListener('change', () => {
          options.style.display = checkbox.checked ? 'block' : 'none';
          this.updatePreview();
        });
      }
    });
  }

  // Ouvrir la modal des règles
  openRulesModal() {
    const modal = document.getElementById('astrological-rules-modal');
    if (modal) {
      modal.style.display = 'block';
      this.refreshRulesList();
    }
  }

  // Fermer la modal des règles
  closeRulesModal() {
    const modal = document.getElementById('astrological-rules-modal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  // Rafraîchir la liste des règles
  refreshRulesList() {
    const rulesList = document.getElementById('rules-list');
    if (!rulesList) return;

    if (this.rules.length === 0) {
      rulesList.innerHTML = '';
      return;
    }

    rulesList.innerHTML = this.rules.map((rule, index) => `
      <div class="rule-item" data-rule-index="${index}">
        <div class="rule-description">
          ${this.getRuleDescription(rule)}
          → ${this.getEffectsDescription(rule.effects)}
        </div>
        <div class="rule-actions-inline">
          <button class="rule-toggle-btn ${rule.active ? '' : 'disabled'}"
                  data-action="toggle" data-index="${index}">
            ${rule.active ? 'Actif' : 'Inactif'}
          </button>
          <button class="rule-edit-btn" data-action="edit" data-index="${index}">
            Modifier
          </button>
          <button class="rule-delete-btn" data-action="delete" data-index="${index}">
            Supprimer
          </button>
        </div>
      </div>
    `).join('');

    // Les gestionnaires d'événements sont attachés une seule fois lors de l'initialisation
  }

  // Attacher les gestionnaires d'événements aux boutons des règles
  attachRuleButtonEvents() {
    const rulesList = document.getElementById('rules-list');
    if (!rulesList) return;

    // Utiliser la délégation d'événements pour gérer tous les boutons
    rulesList.addEventListener('click', (event) => {
      const button = event.target;
      const action = button.getAttribute('data-action');
      const index = parseInt(button.getAttribute('data-index'));

      if (action && !isNaN(index)) {
        switch (action) {
          case 'toggle':
            this.toggleRule(index);
            break;
          case 'edit':
            this.editRule(index);
            break;
          case 'delete':
            this.deleteRule(index);
            break;
        }
      }
    });
  }

  // Obtenir le nom de la planète
  getPlanetName(planetKey) {
    const planetNames = {
      sun: 'Soleil', moon: 'Lune', mercury: 'Mercure', venus: 'Vénus',
      mars: 'Mars', jupiter: 'Jupiter', saturn: 'Saturne',
      uranus: 'Uranus', neptune: 'Neptune', pluto: 'Pluton'
    };
    return planetNames[planetKey] || planetKey;
  }

  // Obtenir la description des effets
  getEffectsDescription(effects) {
    const descriptions = [];
    if (effects.sectorColor) descriptions.push('Coloration secteur');
    if (effects.planetScale) descriptions.push(`Agrandissement ${effects.planetScale.factor}x`);
    if (effects.sectorHighlight) descriptions.push(`Surlignage ${effects.sectorHighlight.style}`);
    if (effects.planetGlow) descriptions.push('Lueur planète');
    return descriptions.join(', ') || 'Aucun effet';
  }

  // Obtenir la description d'une règle
  getRuleDescription(rule) {
    // Support pour l'ancien format (rétrocompatibilité)
    if (rule.planet && rule.sector) {
      return `<strong>${this.getPlanetName(rule.planet)}</strong> dans le secteur <strong>${rule.sector}</strong>`;
    }

    // Nouveau format avec conditions multiples
    if (rule.conditions && rule.conditions.length > 0) {
      const conditionDescriptions = rule.conditions.map(condition => {
        if (condition.type === 'position') {
          return `<strong>${this.getPlanetName(condition.planet)}</strong> dans le secteur <strong>${condition.sector}</strong>`;
        } else if (condition.type === 'aspect') {
          const transitName = this.getPlanetName(condition.transitPlanet);
          const natalName = this.getPlanetName(condition.natalPlanet);
          const aspectName = this.getAspectName(condition.aspectType);
          return `<strong>${transitName}</strong> ${aspectName} <strong>${natalName} natal</strong>`;
        }
        return 'Condition inconnue';
      });

      return conditionDescriptions.join(' <strong>ET</strong> ');
    }

    return 'Règle invalide';
  }

  // Obtenir le nom d'un aspect
  getAspectName(aspectType) {
    const aspectNames = {
      'conjunction': 'en conjonction avec',
      'victoire-martial': 'en victoire martial avec',
      'trigone-2': 'en trigone 2 avec',
      'sextile': 'en sextile avec',
      'square': 'en carré avec',
      'trine': 'en trigone avec',
      'trigone-3': 'en trigone 3 avec',
      'opposition': 'en opposition avec'
    };
    return aspectNames[aspectType] || aspectType;
  }

  // Ajouter une nouvelle règle
  addNewRule() {
    const conditionType = document.getElementById('condition-type').value;
    const conditions = this.collectConditions(conditionType);
    const effects = this.collectSelectedEffects();

    if (conditions.length === 0) {
      alert('Veuillez définir au moins une condition pour la règle.');
      return;
    }

    if (Object.keys(effects).length === 0) {
      alert('Veuillez sélectionner au moins un effet pour la règle.');
      return;
    }

    const newRule = {
      id: Date.now(),
      type: conditionType,
      conditions: conditions,
      effects: effects,
      active: true
    };

    this.rules.push(newRule);
    this.saveRulesToStorage();
    this.refreshRulesList();
    this.resetForm();
    this.applyAllRules();

    // Message de confirmation
    this.showMessage('Règle ajoutée avec succès !', 'success');
  }

  // Collecter les effets sélectionnés
  collectSelectedEffects() {
    const effects = {};

    // Coloration du secteur
    if (document.getElementById('effect-sector-color').checked) {
      effects.sectorColor = {
        color: document.getElementById('sector-color-picker').value
      };
    }

    // Agrandissement de la planète
    if (document.getElementById('effect-planet-scale').checked) {
      effects.planetScale = {
        factor: parseFloat(document.getElementById('planet-scale-slider').value)
      };
    }

    // Surlignage du secteur
    if (document.getElementById('effect-sector-highlight').checked) {
      effects.sectorHighlight = {
        style: document.getElementById('highlight-style').value
      };
    }

    // Lueur de la planète
    if (document.getElementById('effect-planet-glow').checked) {
      effects.planetGlow = {
        color: document.getElementById('planet-glow-color').value
      };
    }

    return effects;
  }

  // Tester la règle actuelle
  testCurrentRule() {
    const planet = document.getElementById('rule-planet').value;
    const sector = parseInt(document.getElementById('rule-sector').value);
    const effects = this.collectSelectedEffects();

    if (Object.keys(effects).length === 0) {
      alert('Veuillez sélectionner au moins un effet pour tester la règle.');
      return;
    }

    // Appliquer temporairement les effets
    this.applyRuleEffects(planet, sector, effects, true);

    // Message de test
    this.showMessage(`Test de la règle : ${this.getPlanetName(planet)} dans le secteur ${sector}`, 'info');

    // Retirer les effets de test après 3 secondes
    setTimeout(() => {
      this.clearTestEffects();
      this.applyAllRules();
    }, 3000);
  }

  // Réinitialiser le formulaire
  resetForm() {
    document.getElementById('rule-planet').value = 'sun';
    document.getElementById('rule-sector').value = '1';

    // Décocher tous les effets
    document.querySelectorAll('.effect-checkbox input[type="checkbox"]').forEach(cb => {
      cb.checked = false;
      const optionsId = cb.id.replace('effect-', '') + '-options';
      const options = document.getElementById(optionsId);
      if (options) options.style.display = 'none';
    });

    // Réinitialiser les valeurs par défaut
    document.getElementById('sector-color-picker').value = '#00ff00';
    document.getElementById('planet-scale-slider').value = '2';
    document.getElementById('planet-scale-value').textContent = '2x';
    document.getElementById('highlight-style').value = 'glow';
    document.getElementById('planet-glow-color').value = '#ffff00';

    this.updatePreview();
  }

  // Mettre à jour l'aperçu
  updatePreview() {
    const preview = document.getElementById('rule-preview');
    if (!preview) return;

    const conditionType = document.getElementById('condition-type').value;
    const conditions = this.collectConditions(conditionType);
    const effects = this.collectSelectedEffects();

    if (conditions.length === 0) {
      preview.innerHTML = '<p>Définissez au moins une condition pour voir l\'aperçu</p>';
      return;
    }

    if (Object.keys(effects).length === 0) {
      preview.innerHTML = '<p>Sélectionnez des effets pour voir l\'aperçu</p>';
      return;
    }

    // Créer la description des conditions
    const conditionDescriptions = conditions.map(condition => {
      if (condition.type === 'position') {
        const planetName = this.getPlanetName(condition.planet);
        return `${planetName} dans le secteur ${condition.sector}`;
      } else if (condition.type === 'aspect') {
        const transitName = this.getPlanetName(condition.transitPlanet);
        const natalName = this.getPlanetName(condition.natalPlanet);
        const aspectName = this.getAspectName(condition.aspectType);
        return `${transitName} ${aspectName} ${natalName} natal`;
      }
      return 'Condition inconnue';
    });

    const conditionsText = conditionDescriptions.join(' ET ');
    const effectsDesc = this.getEffectsDescription(effects);

    preview.innerHTML = `
      <div style="text-align: left;">
        <h5>Aperçu de la règle :</h5>
        <p><strong>Conditions :</strong> ${conditionsText}</p>
        <p><strong>Effets :</strong> ${effectsDesc}</p>
      </div>
    `;
  }

  // Afficher un message
  showMessage(text, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 10001;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      animation: slideIn 0.3s ease;
    `;

    switch (type) {
      case 'success':
        messageDiv.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        break;
      case 'error':
        messageDiv.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
        break;
      default:
        messageDiv.style.background = 'linear-gradient(135deg, #007bff, #0056b3)';
    }

    messageDiv.textContent = text;
    document.body.appendChild(messageDiv);

    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => messageDiv.remove(), 300);
      }
    }, 3000);
  }

  // Sauvegarder les règles
  async saveRulesToStorage() {
    try {
      await StorageMigration.saveAstrologicalRules(this.rules);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des règles:', error);
      // Fallback vers localStorage en cas d'erreur
      localStorage.setItem('astrologicalRules', JSON.stringify(this.rules));
    }
  }

  // Charger les règles
  async loadRulesFromStorage() {
    try {
      this.rules = await StorageMigration.loadAstrologicalRules();
    } catch (error) {
      console.error('Erreur lors du chargement des règles:', error);
      // Fallback vers localStorage en cas d'erreur
      const saved = localStorage.getItem('astrologicalRules');
      this.rules = saved ? JSON.parse(saved) : [];
    }
  }

  // Basculer l'état d'une règle
  toggleRule(index) {
    if (this.rules[index]) {
      this.rules[index].active = !this.rules[index].active;
      this.saveRulesToStorage();
      this.refreshRulesList();
      this.applyAllRules();
    }
  }

  // Supprimer une règle
  deleteRule(index) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette règle ?')) {
      this.rules.splice(index, 1);
      this.saveRulesToStorage();
      this.refreshRulesList();
      this.applyAllRules();
    }
  }

  // Modifier une règle
  editRule(index) {
    const rule = this.rules[index];
    if (!rule) return;

    // Configurer les effets d'abord (cela va réinitialiser le formulaire)
    this.loadEffectsIntoForm(rule.effects);

    // Puis remplir le formulaire avec les valeurs de la règle (après la réinitialisation)
    document.getElementById('rule-planet').value = rule.planet;
    document.getElementById('rule-sector').value = rule.sector;

    // Mettre à jour l'aperçu avec les bonnes valeurs
    this.updatePreview();

    // Supprimer l'ancienne règle
    this.rules.splice(index, 1);
    this.saveRulesToStorage();
    this.refreshRulesList();

    this.showMessage('Règle chargée pour modification', 'info');
  }

  // Charger les effets dans le formulaire
  loadEffectsIntoForm(effects) {
    // Réinitialiser seulement les effets, pas les valeurs de planète et secteur
    this.resetEffectsOnly();

    if (effects.sectorColor) {
      document.getElementById('effect-sector-color').checked = true;
      document.getElementById('sector-color-options').style.display = 'block';
      document.getElementById('sector-color-picker').value = effects.sectorColor.color;
    }

    if (effects.planetScale) {
      document.getElementById('effect-planet-scale').checked = true;
      document.getElementById('planet-scale-options').style.display = 'block';
      document.getElementById('planet-scale-slider').value = effects.planetScale.factor;
      document.getElementById('planet-scale-value').textContent = effects.planetScale.factor + 'x';
    }

    if (effects.sectorHighlight) {
      document.getElementById('effect-sector-highlight').checked = true;
      document.getElementById('sector-highlight-options').style.display = 'block';
      document.getElementById('highlight-style').value = effects.sectorHighlight.style;
    }

    if (effects.planetGlow) {
      document.getElementById('effect-planet-glow').checked = true;
      document.getElementById('planet-glow-options').style.display = 'block';
      document.getElementById('planet-glow-color').value = effects.planetGlow.color;
    }
  }

  // Réinitialiser seulement les effets (pas les valeurs de planète et secteur)
  resetEffectsOnly() {
    // Décocher tous les effets
    document.querySelectorAll('.effect-checkbox input[type="checkbox"]').forEach(cb => {
      cb.checked = false;
      const optionsId = cb.id.replace('effect-', '') + '-options';
      const options = document.getElementById(optionsId);
      if (options) options.style.display = 'none';
    });

    // Réinitialiser les valeurs par défaut des effets
    document.getElementById('sector-color-picker').value = '#00ff00';
    document.getElementById('planet-scale-slider').value = '2';
    document.getElementById('planet-scale-value').textContent = '2x';
    document.getElementById('highlight-style').value = 'glow';
    document.getElementById('planet-glow-color').value = '#ffff00';
  }

  // Appliquer toutes les règles actives
  applyAllRules() {
    // Nettoyer les effets précédents
    this.clearAllEffects();

    // Utiliser les positions de transit globales si disponibles
    const transitPositions = window.transitPositions || this.getCurrentTransitPositions();
    const birthPositions = window.birthPositions || {};

    // Appliquer chaque règle active
    this.rules.forEach((rule, index) => {
      if (!rule.active) return;

      // Support pour l'ancien format (rétrocompatibilité)
      if (rule.planet && rule.sector) {
        if (transitPositions[rule.planet]) {
          const planetData = transitPositions[rule.planet];
          const positionInSign = planetData.degree;
          const planetSector = Math.floor(positionInSign / 2.5) + 1;

          if (planetSector === rule.sector) {
            this.applyRuleEffects(rule.planet, rule.sector, rule.effects, false);
          }
        }
        return;
      }

      // Nouveau format avec conditions multiples
      if (rule.conditions && rule.conditions.length > 0) {
        const allConditionsMet = rule.conditions.every(condition => {
          return this.checkCondition(condition, transitPositions, birthPositions);
        });

        if (allConditionsMet) {
          // Appliquer les effets (utiliser la première condition pour les paramètres legacy)
          const firstCondition = rule.conditions[0];
          if (firstCondition.type === 'position') {
            this.applyRuleEffects(firstCondition.planet, firstCondition.sector, rule.effects, false);
          } else {
            // Pour les aspects, appliquer les effets sur la planète de transit
            this.applyRuleEffects(firstCondition.transitPlanet, 1, rule.effects, false);
          }
        }
      }
    });
  }

  // Vérifier et appliquer les règles actives (appelée depuis le système principal)
  checkActiveRules() {
    if (!window.transitPositions) return;

    // Effacer les effets précédents
    window.activeRuleEffects = new Map();

    // Vérifier chaque règle active
    this.rules.forEach((rule, index) => {
      if (!rule.active) return;

      const planetData = window.transitPositions[rule.planet];
      if (!planetData) return;

      // Calculer le secteur en mode 30° (secteurs de 2.5°)
      // Utiliser la propriété degree qui contient déjà la position dans le signe (0-30°)
      const positionInSign = planetData.degree;
      const currentSector = Math.floor(positionInSign / 2.5) + 1;

      // Si la planète est dans le secteur de la règle, appliquer les effets
      if (currentSector === rule.sector) {
        window.activeRuleEffects.set(index, {
          planet: rule.planet,
          sector: rule.sector,
          effects: rule.effects
        });

        // Appliquer les effets
        this.applyRuleEffects(rule.planet, rule.sector, rule.effects);
      }
    });

    // Redessiner le zodiaque avec les nouveaux effets
    this.redrawZodiacWithEffects();
  }

  // Obtenir les positions actuelles des planètes de transit
  getCurrentTransitPositions() {
    // Cette fonction doit être intégrée avec le système existant
    // Pour l'instant, on retourne un objet vide
    // TODO: Intégrer avec le système de calcul des positions
    return {};
  }

  // Obtenir le secteur d'une planète selon sa position dans le mode 30°
  getPlanetSector(planetData) {
    // En mode 30°, utiliser directement la propriété degree qui contient la position dans le signe (0-30°)
    const positionInSign = planetData.degree;

    // Diviser par 2.5° pour obtenir le secteur (1-12)
    // Secteur 1: 0-2.5°, Secteur 2: 2.5-5°, etc.
    return Math.floor(positionInSign / 2.5) + 1;
  }

  // Appliquer les effets d'une règle
  applyRuleEffects(planet, sector, effects, isTest = false) {
    const canvas = document.getElementById('circular-zodiac-canvas');
    if (!canvas) return;

    const effectKey = `${planet}-${sector}`;

    // Stocker les effets actifs
    if (!isTest) {
      this.activeEffects.set(effectKey, effects);
    }

    // Stocker les effets pour le système de dessin
    if (!window.activeRuleEffects) {
      window.activeRuleEffects = new Map();
    }

    if (isTest) {
      window.testRuleEffects = effects;
      window.testRulePlanet = planet;
      window.testRuleSector = sector;
    } else {
      window.activeRuleEffects.set(effectKey, {
        planet: planet,
        sector: sector,
        effects: effects
      });
    }

    // Redessiner le zodiaque avec les effets
    this.redrawZodiacWithEffects();
  }

  // Redessiner le zodiaque avec les effets
  redrawZodiacWithEffects() {
    // Utiliser le système de dessin existant
    if (window.birthPositions && window.transitPositions && window.drawCircularProportionalZodiac) {
      window.drawCircularProportionalZodiac(window.birthPositions, window.transitPositions);
    }
  }

  // Nettoyer tous les effets
  clearAllEffects() {
    // Nettoyer les effets stockés
    if (window.activeRuleEffects) {
      window.activeRuleEffects.clear();
    }
    this.activeEffects.clear();

    // Redessiner le zodiaque sans effets
    this.redrawZodiacWithEffects();
  }

  // Nettoyer uniquement les effets de test
  clearTestEffects() {
    // Nettoyer les effets de test
    window.testRuleEffects = null;
    window.testRulePlanet = null;
    window.testRuleSector = null;

    // Redessiner le zodiaque
    this.redrawZodiacWithEffects();
  }

  // Méthode publique pour être appelée depuis le système principal
  onChartUpdate(transitPositions) {
    // Mettre à jour les positions et appliquer les règles
    this.currentTransitPositions = transitPositions;
    this.applyAllRules();
  }

  // Obtenir les positions actuelles (version mise à jour)
  getCurrentTransitPositions() {
    return this.currentTransitPositions || {};
  }

  // Basculer entre les types de conditions
  toggleConditionType() {
    const conditionType = document.getElementById('condition-type').value;
    const positionConditions = document.getElementById('position-conditions');
    const aspectConditions = document.getElementById('aspect-conditions');

    if (conditionType === 'position') {
      positionConditions.style.display = 'block';
      aspectConditions.style.display = 'none';
    } else {
      positionConditions.style.display = 'none';
      aspectConditions.style.display = 'block';
    }

    this.updatePreview();
  }

  // Ajouter une condition de position
  addPositionCondition() {
    const container = document.getElementById('position-conditions');
    const conditionGroups = container.querySelectorAll('.condition-group');
    const newIndex = conditionGroups.length;

    const newCondition = document.createElement('div');
    newCondition.className = 'condition-group';
    newCondition.setAttribute('data-condition-index', newIndex);

    newCondition.innerHTML = `
      <div class="condition-row">
        <label>Quand la planète de transit</label>
        <select class="rule-planet rule-select">
          <option value="sun">Soleil</option>
          <option value="moon">Lune</option>
          <option value="mercury">Mercure</option>
          <option value="venus">Vénus</option>
          <option value="mars">Mars</option>
          <option value="jupiter">Jupiter</option>
          <option value="saturn">Saturne</option>
          <option value="uranus">Uranus</option>
          <option value="neptune">Neptune</option>
          <option value="pluto">Pluton</option>
        </select>
        <label>est dans le secteur</label>
        <select class="rule-sector rule-select">
          <option value="1">Secteur 1 : 0° - 2.5°</option>
          <option value="2">Secteur 2 : 2.5° - 5°</option>
          <option value="3">Secteur 3 : 5° - 7.5°</option>
          <option value="4">Secteur 4 : 7.5° - 10°</option>
          <option value="5">Secteur 5 : 10° - 12.5°</option>
          <option value="6">Secteur 6 : 12.5° - 15°</option>
          <option value="7">Secteur 7 : 15° - 17.5°</option>
          <option value="8">Secteur 8 : 17.5° - 20°</option>
          <option value="9">Secteur 9 : 20° - 22.5°</option>
          <option value="10">Secteur 10 : 22.5° - 25°</option>
          <option value="11">Secteur 11 : 25° - 27.5°</option>
          <option value="12">Secteur 12 : 27.5° - 30°</option>
        </select>
        <button type="button" class="remove-condition-btn">✕</button>
      </div>
    `;

    // Insérer avant le bouton d'ajout
    const addButton = document.getElementById('add-position-condition');
    container.insertBefore(newCondition, addButton);

    // Attacher le gestionnaire d'événement au nouveau bouton de suppression
    const removeBtn = newCondition.querySelector('.remove-condition-btn');
    if (removeBtn) {
      removeBtn.addEventListener('click', () => this.removeCondition(removeBtn));
    }

    // Afficher les boutons de suppression
    this.updateRemoveButtons('position-conditions');
    this.updatePreview();
  }

  // Ajouter une condition d'aspect
  addAspectCondition() {
    const container = document.getElementById('aspect-conditions');
    const conditionGroups = container.querySelectorAll('.condition-group');
    const newIndex = conditionGroups.length;

    const newCondition = document.createElement('div');
    newCondition.className = 'condition-group';
    newCondition.setAttribute('data-condition-index', newIndex);

    newCondition.innerHTML = `
      <div class="condition-row">
        <label>Quand</label>
        <select class="aspect-transit-planet rule-select">
          <option value="sun">Soleil</option>
          <option value="moon">Lune</option>
          <option value="mercury">Mercure</option>
          <option value="venus">Vénus</option>
          <option value="mars">Mars</option>
          <option value="jupiter">Jupiter</option>
          <option value="saturn">Saturne</option>
          <option value="uranus">Uranus</option>
          <option value="neptune">Neptune</option>
          <option value="pluto">Pluton</option>
        </select>
        <label>fait un</label>
        <select class="aspect-type rule-select">
          <option value="conjunction">Conjonction (0°)</option>
          <option value="victoire-martial">Victoire Martial (16°)</option>
          <option value="trigone-2">Trigone 2 (40°)</option>
          <option value="sextile">Sextile (60°)</option>
          <option value="square">Carré (90°)</option>
          <option value="trine">Trigone (120°)</option>
          <option value="trigone-3">Trigone 3 (162°)</option>
          <option value="opposition">Opposition (180°)</option>
        </select>
        <label>avec</label>
        <select class="aspect-natal-planet rule-select">
          <option value="sun">Soleil natal</option>
          <option value="moon">Lune natale</option>
          <option value="mercury">Mercure natal</option>
          <option value="venus">Vénus natale</option>
          <option value="mars">Mars natal</option>
          <option value="jupiter">Jupiter natal</option>
          <option value="saturn">Saturne natal</option>
          <option value="uranus">Uranus natal</option>
          <option value="neptune">Neptune natal</option>
          <option value="pluto">Pluton natal</option>
        </select>
        <button type="button" class="remove-condition-btn">✕</button>
      </div>
    `;

    // Insérer avant le bouton d'ajout
    const addButton = document.getElementById('add-aspect-condition');
    container.insertBefore(newCondition, addButton);

    // Attacher le gestionnaire d'événement au nouveau bouton de suppression
    const removeBtn = newCondition.querySelector('.remove-condition-btn');
    if (removeBtn) {
      removeBtn.addEventListener('click', () => this.removeCondition(removeBtn));
    }

    // Afficher les boutons de suppression
    this.updateRemoveButtons('aspect-conditions');
    this.updatePreview();
  }

  // Supprimer une condition
  removeCondition(button) {
    const conditionGroup = button.closest('.condition-group');
    const container = conditionGroup.closest('.conditions-container');
    conditionGroup.remove();

    // Mettre à jour les boutons de suppression
    this.updateRemoveButtons(container.id);
    this.updatePreview();
  }

  // Configurer les gestionnaires d'événements pour la suppression des conditions
  setupConditionRemovalListeners() {
    // Utiliser la délégation d'événements pour les conteneurs de conditions
    const positionContainer = document.getElementById('position-conditions');
    const aspectContainer = document.getElementById('aspect-conditions');

    if (positionContainer) {
      positionContainer.addEventListener('click', (event) => {
        if (event.target.classList.contains('remove-condition-btn')) {
          this.removeCondition(event.target);
        }
      });
    }

    if (aspectContainer) {
      aspectContainer.addEventListener('click', (event) => {
        if (event.target.classList.contains('remove-condition-btn')) {
          this.removeCondition(event.target);
        }
      });
    }

    // Attacher les gestionnaires aux boutons existants dans le HTML
    document.querySelectorAll('.remove-condition-btn').forEach(btn => {
      btn.addEventListener('click', () => this.removeCondition(btn));
    });
  }

  // Mettre à jour l'affichage des boutons de suppression
  updateRemoveButtons(containerId) {
    const container = document.getElementById(containerId);
    const conditionGroups = container.querySelectorAll('.condition-group');

    conditionGroups.forEach((group, index) => {
      const removeBtn = group.querySelector('.remove-condition-btn');
      if (removeBtn) {
        removeBtn.style.display = conditionGroups.length > 1 ? 'inline-block' : 'none';
      }
    });
  }

  // Collecter les conditions selon le type
  collectConditions(type) {
    const conditions = [];

    if (type === 'position') {
      const container = document.getElementById('position-conditions');
      const conditionGroups = container.querySelectorAll('.condition-group');

      conditionGroups.forEach(group => {
        const planet = group.querySelector('.rule-planet').value;
        const sector = parseInt(group.querySelector('.rule-sector').value);
        conditions.push({ type: 'position', planet, sector });
      });
    } else if (type === 'aspect') {
      const container = document.getElementById('aspect-conditions');
      const conditionGroups = container.querySelectorAll('.condition-group');

      conditionGroups.forEach(group => {
        const transitPlanet = group.querySelector('.aspect-transit-planet').value;
        const aspectType = group.querySelector('.aspect-type').value;
        const natalPlanet = group.querySelector('.aspect-natal-planet').value;
        conditions.push({
          type: 'aspect',
          transitPlanet,
          aspectType,
          natalPlanet
        });
      });
    }

    return conditions;
  }

  // Vérifier une condition
  checkCondition(condition, transitPositions, birthPositions) {
    if (condition.type === 'position') {
      if (!transitPositions[condition.planet]) return false;

      const planetData = transitPositions[condition.planet];
      const positionInSign = planetData.degree;
      const planetSector = Math.floor(positionInSign / 2.5) + 1;

      return planetSector === condition.sector;
    }
    else if (condition.type === 'aspect') {
      if (!transitPositions[condition.transitPlanet] || !birthPositions[condition.natalPlanet]) {
        return false;
      }

      // Calculer les positions absolues
      const transitData = transitPositions[condition.transitPlanet];
      const natalData = birthPositions[condition.natalPlanet];

      const transitPos = this.getAbsolutePosition(transitData);
      const natalPos = this.getAbsolutePosition(natalData);

      // Calculer la séparation
      let separation = Math.abs(transitPos - natalPos);
      if (separation > 180) separation = 360 - separation;

      // Obtenir l'angle de l'aspect
      const aspectAngles = {
        'conjunction': 0,
        'victoire-martial': 16,
        'trigone-2': 40,
        'sextile': 60,
        'square': 90,
        'trine': 120,
        'trigone-3': 162,
        'opposition': 180
      };

      const aspectAngle = aspectAngles[condition.aspectType];
      if (aspectAngle === undefined) return false;

      // Vérifier si l'aspect est dans l'orb (utiliser 2° par défaut)
      const orb = 2.0;
      const orbDifference = Math.abs(separation - aspectAngle);

      return orbDifference <= orb;
    }

    return false;
  }

  // Obtenir la position absolue d'une planète (0-360°)
  getAbsolutePosition(planetData) {
    if (typeof planetData === 'number') {
      return planetData; // Déjà en position absolue
    }

    if (planetData.sign && planetData.degree !== undefined) {
      const signs = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                    'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];
      const signIndex = signs.indexOf(planetData.sign);
      return signIndex * 30 + parseFloat(planetData.degree);
    }

    return 0;
  }
}

// Ajouter les styles d'animation CSS dynamiquement
const styleSheet = document.createElement('style');
styleSheet.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(styleSheet);

// Instance globale du système de règles
let astroRules;

// Initialisation quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
  astroRules = new AstrologicalRulesSystem();
  // Exposer globalement pour l'intégration avec le système principal
  window.astroRules = astroRules;
});
