// Système de prédictions automatiques pour le mois suivant
class MonthlyPredictionsSystem {
  constructor() {
    this.currentMonthData = new Map(); // Stockage des données du mois actuel
    this.aspectDatabase = new Map(); // Base de données d'aspects avec leurs valeurs associées
    this.predictionWeights = {
      positivity: { conjunction: 2, opposition: -1, sextile: 1, square: -1, trine: 2 },
      importance: { conjunction: 3, opposition: 2, sextile: 1, square: 2, trine: 1 },
      surprise: { conjunction: 1, opposition: 3, sextile: 1, square: 2, trine: 1 }
    };
  }

  // Analyser les données du mois actuel
  async analyzeCurrentMonth() {
    console.log('🔍 Analyse du mois actuel pour les prédictions...');

    const currentMonthBody = document.getElementById('current-month-body');
    if (!currentMonthBody) return;

    // Récupérer les données des 3 premières lignes (résumé)
    const summaryData = this.extractSummaryData(currentMonthBody);

    // Calculer les aspects réels pour chaque jour du mois actuel
    const currentMonthAspects = await this.calculateCurrentMonthAspects();

    // Créer la base de données d'aspects avec leurs valeurs associées
    this.createAspectDatabase(currentMonthAspects, summaryData);

    console.log('📊 Analyse terminée:', {
      summaryData: summaryData.size,
      aspectDatabase: this.aspectDatabase.size
    });
  }

  // Calculer les aspects du mois actuel
  async calculateCurrentMonthAspects() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    const aspectsData = new Map();

    // Obtenir les positions de naissance
    const birthDate = new Date(document.getElementById('birth-date').value);
    birthDate.setHours(
      parseInt(document.getElementById('birth-hour').value) || 0,
      parseInt(document.getElementById('birth-minute').value) || 0,
      0
    );
    const birthPositions = calculatePlanetaryPositions(birthDate);

    // Obtenir les paramètres de filtrage actuels
    const selectedAspectTypes = this.getSelectedAspectTypes('current-month');
    const selectedOrb = this.getSelectedOrb('current-month');

    // Calculer pour chaque jour du mois actuel
    for (let day = 1; day <= daysInMonth; day++) {
      const transitDate = new Date(year, month, day, 12, 0, 0);
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Calculer les aspects pour ce jour
      const dayAspects = this.calculateDayAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes);

      aspectsData.set(day, dayAspects);
    }

    return aspectsData;
  }

  // Créer la base de données d'aspects avec leurs valeurs associées
  createAspectDatabase(currentMonthAspects, summaryData) {
    this.aspectDatabase.clear();

    // Pour chaque jour du mois actuel
    currentMonthAspects.forEach((dayAspects, day) => {
      // Récupérer les valeurs de résumé pour ce jour
      const dayValues = {
        'general-positivity': summaryData.get('general-positivity')?.get(day) || null,
        'event-importance': summaryData.get('event-importance')?.get(day) || null,
        'surprise-level': summaryData.get('surprise-level')?.get(day) || null
      };

      // Pour chaque aspect de ce jour
      dayAspects.forEach(aspect => {
        // Créer une clé unique pour cet aspect
        const aspectKey = this.createAspectKey(aspect);

        // Ajouter cet aspect à la base de données
        if (!this.aspectDatabase.has(aspectKey)) {
          this.aspectDatabase.set(aspectKey, []);
        }

        this.aspectDatabase.get(aspectKey).push({
          day: day,
          values: dayValues,
          aspect: aspect
        });
      });
    });

    console.log(`📊 Base de données créée avec ${this.aspectDatabase.size} aspects uniques`);
  }

  // Créer une clé unique pour un aspect
  createAspectKey(aspect) {
    return `${aspect.transitPlanet}-${aspect.aspectType}-${aspect.natalPlanet}`;
  }

  // Extraire les données de résumé (3 premières lignes)
  extractSummaryData(tbody) {
    const summaryData = new Map();
    const summaryTypes = ['general-positivity', 'event-importance', 'surprise-level'];

    summaryTypes.forEach(type => {
      const cells = tbody.querySelectorAll(`[data-summary-type="${type}"]`);
      const dayValues = new Map();

      cells.forEach(cell => {
        const day = parseInt(cell.getAttribute('data-day'));
        const value = this.parseValue(cell.textContent);
        if (value !== null) {
          dayValues.set(day, value);
        }
      });

      summaryData.set(type, dayValues);
    });

    return summaryData;
  }

  // Extraire les données d'aspects des planètes
  extractAspectsData(tbody) {
    const aspectsData = new Map();
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    planets.forEach(planet => {
      const cells = tbody.querySelectorAll(`[data-planet="${planet}"]`);
      const dayValues = new Map();

      cells.forEach(cell => {
        const day = parseInt(cell.getAttribute('data-day'));
        const count = this.parseValue(cell.textContent);
        if (count !== null && count > 0) {
          dayValues.set(day, count);
        }
      });

      aspectsData.set(planet, dayValues);
    });

    return aspectsData;
  }

  // Parser une valeur de cellule
  parseValue(text) {
    if (!text || text === '-' || text.trim() === '') return null;
    const num = parseInt(text.trim());
    return isNaN(num) ? null : num;
  }

  // Identifier les patterns entre aspects et niveaux de résumé
  identifyPatterns(summaryData, aspectsData) {
    this.aspectPatterns.clear();

    // Pour chaque type de résumé
    summaryData.forEach((dayValues, summaryType) => {
      const patterns = new Map();

      // Pour chaque jour avec une valeur de résumé
      dayValues.forEach((summaryValue, day) => {
        // Analyser les aspects de ce jour
        const dayAspects = this.getDayAspects(day, aspectsData);

        if (dayAspects.total > 0) {
          // Créer une signature d'aspects pour ce jour
          const aspectSignature = this.createAspectSignature(dayAspects);

          // Stocker la corrélation
          if (!patterns.has(aspectSignature)) {
            patterns.set(aspectSignature, []);
          }
          patterns.get(aspectSignature).push({
            day: day,
            value: summaryValue,
            aspects: dayAspects
          });
        }
      });

      this.aspectPatterns.set(summaryType, patterns);
    });
  }

  // Obtenir les aspects détaillés d'un jour spécifique
  getDayAspects(day, aspectsData) {
    // Cette méthode sera remplacée par l'analyse des aspects réels
    // Pour l'instant, on utilise les données du tableau existant
    const dayAspects = { total: 0, planets: {}, aspects: [] };

    aspectsData.forEach((dayValues, planet) => {
      const count = dayValues.get(day) || 0;
      dayAspects.planets[planet] = count;
      dayAspects.total += count;
    });

    return dayAspects;
  }

  // Créer une signature d'aspects pour identifier les patterns
  createAspectSignature(dayAspects) {
    // Si on a des aspects détaillés, les utiliser
    if (dayAspects.aspects && dayAspects.aspects.length > 0) {
      // Grouper par type d'aspect et planètes impliquées
      const aspectGroups = {};
      dayAspects.aspects.forEach(aspect => {
        const key = `${aspect.aspectType}-${aspect.transitPlanet}-${aspect.natalPlanet}`;
        aspectGroups[key] = (aspectGroups[key] || 0) + 1;
      });

      // Créer une signature basée sur les aspects les plus fréquents
      const signature = Object.entries(aspectGroups)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5) // Top 5 aspects
        .map(([key, count]) => `${key}:${count}`)
        .join('|');

      return signature || 'no-aspects';
    }

    // Fallback : utiliser les planètes les plus actives
    const activePlanets = Object.entries(dayAspects.planets)
      .filter(([planet, count]) => count > 0)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3) // Top 3 planètes les plus actives
      .map(([planet, count]) => `${planet}:${count}`)
      .join('|');

    return activePlanets || 'no-aspects';
  }

  // Prédire les valeurs pour le mois suivant
  async predictNextMonth() {
    console.log('🔮 Génération des prédictions pour le mois suivant...');

    // Analyser d'abord le mois actuel
    this.analyzeCurrentMonth();

    // Calculer les aspects du mois suivant
    const nextMonthAspects = await this.calculateNextMonthAspects();

    // Générer les prédictions
    const predictions = this.generatePredictions(nextMonthAspects);

    // Appliquer les prédictions au tableau
    this.applyPredictionsToTable(predictions);

    console.log('✨ Prédictions appliquées avec succès !');
  }

  // Calculer les aspects du mois suivant
  async calculateNextMonthAspects() {
    const currentDate = new Date();
    const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    const year = nextMonth.getFullYear();
    const month = nextMonth.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    const aspectsData = new Map();
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    // Obtenir les positions de naissance
    const birthDate = new Date(document.getElementById('birth-date').value);
    birthDate.setHours(
      parseInt(document.getElementById('birth-hour').value) || 0,
      parseInt(document.getElementById('birth-minute').value) || 0,
      0
    );
    const birthPositions = calculatePlanetaryPositions(birthDate);

    // Obtenir les paramètres de filtrage actuels
    const selectedAspectTypes = this.getSelectedAspectTypes('next-month');
    const selectedOrb = this.getSelectedOrb('next-month');

    // Calculer pour chaque jour du mois suivant
    for (let day = 1; day <= daysInMonth; day++) {
      const transitDate = new Date(year, month, day, 12, 0, 0);
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Calculer les aspects pour ce jour
      const dayAspects = this.calculateDayAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes);

      // Structurer les données avec les aspects détaillés
      const dayData = {
        total: dayAspects.length,
        planets: {},
        aspects: dayAspects // Stocker les aspects détaillés
      };

      // Compter les aspects par planète natale
      planets.forEach(planet => {
        const count = dayAspects.filter(aspect => aspect.natalPlanet === planet).length;
        dayData.planets[planet] = count;
      });

      aspectsData.set(day, dayData);
    }

    return aspectsData;
  }

  // Générer les prédictions basées sur la base de données d'aspects
  generatePredictions(nextMonthAspects) {
    const predictions = new Map();
    const summaryTypes = ['general-positivity', 'event-importance', 'surprise-level'];

    summaryTypes.forEach(summaryType => {
      const dayPredictions = new Map();

      // Pour chaque jour du mois suivant
      nextMonthAspects.forEach((dayAspects, day) => {
        const aspectsArray = dayAspects.aspects || [];

        // Analyser chaque aspect individuellement
        const aspectAnalysis = this.analyzeAspectsForDay(aspectsArray, summaryType);

        // Calculer la prédiction basée sur l'analyse
        const prediction = this.calculatePredictionFromAnalysis(aspectAnalysis, summaryType, day);

        dayPredictions.set(day, prediction);
      });

      predictions.set(summaryType, dayPredictions);
    });

    return predictions;
  }

  // Analyser les aspects d'un jour pour un type de résumé donné
  analyzeAspectsForDay(aspectsArray, summaryType) {
    const analysis = {
      matchingAspects: [],
      totalOccurrences: 0,
      averageValue: 0,
      confidence: 0,
      detailedBreakdown: []
    };

    if (!aspectsArray || aspectsArray.length === 0) {
      return analysis;
    }

    let totalValue = 0;
    let totalCount = 0;

    // Analyser chaque aspect
    aspectsArray.forEach(aspect => {
      const aspectKey = this.createAspectKey(aspect);
      const historicalData = this.aspectDatabase.get(aspectKey);

      if (historicalData && historicalData.length > 0) {
        // Filtrer les occurrences qui ont une valeur pour ce type de résumé
        const relevantOccurrences = historicalData.filter(data =>
          data.values[summaryType] !== null && data.values[summaryType] !== undefined
        );

        if (relevantOccurrences.length > 0) {
          // Calculer la moyenne pour cet aspect
          const aspectValues = relevantOccurrences.map(data => data.values[summaryType]);
          const aspectAverage = aspectValues.reduce((sum, val) => sum + val, 0) / aspectValues.length;

          totalValue += aspectAverage * relevantOccurrences.length; // Pondérer par le nombre d'occurrences
          totalCount += relevantOccurrences.length;

          analysis.matchingAspects.push({
            aspect: aspect,
            historicalOccurrences: relevantOccurrences,
            averageValue: aspectAverage,
            occurrenceCount: relevantOccurrences.length
          });

          // Ajouter au breakdown détaillé
          analysis.detailedBreakdown.push({
            aspectKey: aspectKey,
            aspectName: `${this.getPlanetName(aspect.transitPlanet)} ${this.getAspectName(aspect.aspectType)} ${this.getPlanetName(aspect.natalPlanet)}`,
            occurrences: relevantOccurrences,
            averageValue: aspectAverage,
            contribution: aspectAverage * relevantOccurrences.length
          });
        }
      }
    });

    // Calculer les moyennes globales
    if (totalCount > 0) {
      analysis.averageValue = totalValue / totalCount;
      analysis.totalOccurrences = totalCount;
      analysis.confidence = Math.min(totalCount / 10, 1); // Confiance basée sur le nombre total d'occurrences
    }

    return analysis;
  }

  // Calculer la prédiction finale basée sur l'analyse
  calculatePredictionFromAnalysis(analysis, summaryType, day) {
    let predictedValue = null; // Pas de valeur par défaut
    let confidence = 0;

    if (analysis.totalOccurrences > 0) {
      predictedValue = Math.round(analysis.averageValue);
      confidence = analysis.confidence;
      // Limiter les valeurs entre 1 et 5
      predictedValue = Math.max(1, Math.min(5, predictedValue));
    }

    return {
      value: predictedValue,
      confidence: confidence,
      aspectsCount: analysis.matchingAspects.length,
      calculationDetails: {
        summaryType: summaryType,
        day: day,
        analysis: analysis,
        usedHistoricalData: analysis.totalOccurrences > 0,
        totalHistoricalOccurrences: analysis.totalOccurrences
      }
    };
  }

  // Calculer les aspects d'un jour spécifique
  calculateDayAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes) {
    const aspects = [];
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    // Définir les aspects et leurs angles en mode 30°
    const aspectDefinitions = {
      'conjunction': { angle: 0, name: 'Conjonction' },
      'sextile': { angle: 5, name: 'Sextile' },
      'square': { angle: 7.5, name: 'Carré' },
      'trine': { angle: 10, name: 'Trigone' },
      'opposition': { angle: 15, name: 'Opposition' }
    };

    planets.forEach(transitPlanet => {
      planets.forEach(natalPlanet => {
        const transitData = transitPositions[transitPlanet];
        const natalData = birthPositions[natalPlanet];

        if (!transitData || !natalData) return;

        // Calculer les positions proportionnelles (0-30°)
        const transitPos = this.getProportionalPosition(transitData);
        const natalPos = this.getProportionalPosition(natalData);

        // Calculer la séparation en mode proportionnel
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 15) separation = 30 - separation; // Gérer le wrap-around à 30°

        // Vérifier chaque type d'aspect sélectionné
        selectedAspectTypes.forEach(aspectType => {
          const aspectDef = aspectDefinitions[aspectType];
          if (aspectDef && Math.abs(separation - aspectDef.angle) <= selectedOrb) {
            aspects.push({
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType,
              aspectName: aspectDef.name,
              orb: Math.abs(separation - aspectDef.angle),
              separation: separation
            });
          }
        });
      });
    });

    return aspects;
  }

  // Obtenir la position proportionnelle (0-30°)
  getProportionalPosition(planetData) {
    // Utiliser la propriété degree qui contient déjà la position dans le signe (0-30°)
    return planetData.degree;
  }

  // Calcul de prédiction générique basé sur les poids
  calculateGenericPrediction(aspectsArray, summaryType) {
    // Analyser les types d'aspects et les planètes impliquées
    let score = 0;
    const aspectWeights = this.predictionWeights[summaryType] || {};

    if (Array.isArray(aspectsArray)) {
      aspectsArray.forEach(aspect => {
        const weight = aspectWeights[aspect.aspectType] || 0;
        score += weight;

        // Bonus pour certaines planètes importantes
        if (['sun', 'moon', 'mars', 'jupiter'].includes(aspect.transitPlanet)) {
          score += 0.5;
        }
        if (['sun', 'moon', 'venus', 'mars'].includes(aspect.natalPlanet)) {
          score += 0.5;
        }
      });
    }

    // Normaliser le score entre 1 et 5
    const normalizedScore = Math.max(1, Math.min(5, Math.round(score / 2) + 2));
    return normalizedScore;
  }

  // Appliquer les prédictions au tableau du mois suivant
  applyPredictionsToTable(predictions) {
    const nextMonthBody = document.getElementById('next-month-body');
    if (!nextMonthBody) return;

    predictions.forEach((dayPredictions, summaryType) => {
      dayPredictions.forEach((prediction, day) => {
        const cell = nextMonthBody.querySelector(`[data-summary-type="${summaryType}"][data-day="${day}"]`);
        if (cell) {
          // Ne pas appliquer de prédiction si aucune donnée historique
          if (prediction.value === null || prediction.value === undefined) {
            // Laisser la cellule vide ou afficher 0
            cell.textContent = '';
            cell.classList.remove('predicted-value', 'high-confidence', 'medium-confidence', 'low-confidence');
            cell.classList.add('summary-editable'); // Rendre éditable
            cell.setAttribute('contenteditable', 'true');
            cell.title = 'Aucune donnée historique pour prédiction';
            return;
          }

          // Appliquer la prédiction
          cell.textContent = prediction.value.toString();

          // Ajouter des classes CSS pour indiquer que c'est une prédiction
          cell.classList.add('predicted-value');
          cell.classList.remove('summary-editable'); // Rendre non-éditable
          cell.setAttribute('contenteditable', 'false');

          // Ajouter un indicateur de confiance
          const confidenceClass = prediction.confidence > 0.7 ? 'high-confidence' :
                                 prediction.confidence > 0.4 ? 'medium-confidence' : 'low-confidence';
          cell.classList.add(confidenceClass);

          // Stocker les détails de calcul dans la cellule
          cell.setAttribute('data-prediction-details', JSON.stringify(prediction.calculationDetails));

          // Ajouter un tooltip avec les détails
          cell.title = `Prédiction: ${prediction.value}\nConfiance: ${Math.round(prediction.confidence * 100)}%\nBasé sur ${prediction.calculationDetails.totalHistoricalOccurrences} occurrences historiques\nCliquez pour voir les détails`;

          // Ajouter l'événement de clic pour afficher les détails
          cell.addEventListener('click', (event) => {
            this.showPredictionDetails(event.target, summaryType, day, prediction);
          });
        }
      });
    });
  }

  // Afficher les détails de calcul d'une prédiction
  showPredictionDetails(cell, summaryType, day, prediction) {
    const details = prediction.calculationDetails;
    const analysis = details.analysis;

    // Créer le popup de détails
    const popup = document.createElement('div');
    popup.className = 'prediction-details-popup';
    popup.innerHTML = `
      <div class="prediction-details-header">
        <h3>🔮 Détails de Prédiction</h3>
        <button class="close-popup-btn">&times;</button>
      </div>
      <div class="prediction-details-content">
        <div class="prediction-summary">
          <h4>${this.getSummaryTypeName(summaryType)} - Jour ${day}</h4>
          <div class="prediction-value">Valeur prédite: <strong>${prediction.value}</strong></div>
          <div class="prediction-confidence">Confiance: <strong>${Math.round(prediction.confidence * 100)}%</strong></div>
          <div class="historical-data">Basé sur <strong>${details.totalHistoricalOccurrences}</strong> occurrences historiques</div>
        </div>

        <div class="calculation-method">
          <h4>📊 Méthode de Calcul</h4>
          <p>${details.usedHistoricalData ?
            '🎯 Basé sur les données historiques du mois actuel' :
            '🧮 Valeur par défaut (aucune donnée historique)'}</p>
        </div>

        ${analysis.matchingAspects.length > 0 ? `
          <div class="historical-aspects">
            <h4>📈 Aspects avec Données Historiques (${analysis.matchingAspects.length})</h4>
            ${this.formatHistoricalAspects(analysis.matchingAspects, summaryType)}
          </div>
        ` : ''}

        <div class="detailed-breakdown">
          <h4>🔍 Analyse Détaillée par Aspect</h4>
          ${this.formatDetailedBreakdown(analysis.detailedBreakdown, summaryType)}
        </div>

        ${analysis.averageValue > 0 ? `
          <div class="calculation-summary">
            <h4>📊 Résumé du Calcul</h4>
            <div class="calc-item">
              <span>Moyenne pondérée:</span>
              <span><strong>${analysis.averageValue.toFixed(2)}</strong></span>
            </div>
            <div class="calc-item">
              <span>Valeur arrondie:</span>
              <span><strong>${prediction.value}</strong></span>
            </div>
            <div class="calc-item">
              <span>Niveau de confiance:</span>
              <span><strong>${Math.round(prediction.confidence * 100)}%</strong></span>
            </div>
          </div>
        ` : ''}
      </div>
    `;

    // Ajouter le popup au document
    document.body.appendChild(popup);

    // Positionner le popup près de la cellule
    const rect = cell.getBoundingClientRect();
    popup.style.position = 'fixed';
    popup.style.left = Math.min(rect.left, window.innerWidth - 400) + 'px';
    popup.style.top = Math.min(rect.bottom + 10, window.innerHeight - 500) + 'px';
    popup.style.zIndex = '10000';

    // Ajouter l'événement de fermeture
    popup.querySelector('.close-popup-btn').addEventListener('click', () => {
      document.body.removeChild(popup);
    });

    // Fermer en cliquant à l'extérieur
    setTimeout(() => {
      document.addEventListener('click', function closePopup(e) {
        if (!popup.contains(e.target) && e.target !== cell) {
          document.body.removeChild(popup);
          document.removeEventListener('click', closePopup);
        }
      });
    }, 100);
  }

  // Obtenir le nom du type de résumé
  getSummaryTypeName(summaryType) {
    const names = {
      'general-positivity': 'Degré de Positivité Générale',
      'event-importance': 'Niveaux Importance des Événements',
      'surprise-level': 'Degré de Surprise'
    };
    return names[summaryType] || summaryType;
  }

  // Formater les aspects avec données historiques
  formatHistoricalAspects(matchingAspects, summaryType) {
    if (!matchingAspects || matchingAspects.length === 0) {
      return '<p class="no-aspects">Aucun aspect avec données historiques</p>';
    }

    return `
      <div class="historical-aspects-list">
        ${matchingAspects.map(item => `
          <div class="historical-aspect-item">
            <div class="aspect-header">
              <span class="aspect-planets">${this.getPlanetName(item.aspect.transitPlanet)} ${this.getAspectName(item.aspect.aspectType)} ${this.getPlanetName(item.aspect.natalPlanet)}</span>
              <span class="aspect-average">Moyenne: <strong>${item.averageValue.toFixed(1)}</strong></span>
            </div>
            <div class="historical-occurrences">
              <small>Occurrences (${item.occurrenceCount}): ${item.historicalOccurrences.map(occ =>
                `Jour ${occ.day} = ${occ.values[summaryType]}`
              ).join(', ')}</small>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  // Formater le breakdown détaillé
  formatDetailedBreakdown(breakdown, summaryType) {
    if (!breakdown || breakdown.length === 0) {
      return '<p class="no-breakdown">Aucune donnée historique disponible</p>';
    }

    return `
      <div class="breakdown-list">
        ${breakdown.map(item => `
          <div class="breakdown-item">
            <div class="breakdown-header">
              <span class="aspect-name">${item.aspectName}</span>
              <span class="contribution">Contribution: <strong>${item.contribution.toFixed(1)}</strong></span>
            </div>
            <div class="breakdown-details">
              <div class="breakdown-stats">
                <span>Moyenne: ${item.averageValue.toFixed(1)}</span>
                <span>Occurrences: ${item.occurrences.length}</span>
              </div>
              <div class="occurrence-details">
                ${item.occurrences.map(occ =>
                  `<span class="occurrence-tag">J${occ.day}: ${occ.values[summaryType]}</span>`
                ).join('')}
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  // Formater le breakdown du score
  formatScoreBreakdown(breakdown) {
    if (!breakdown || breakdown.length === 0) {
      return '<p>Aucun détail de score disponible</p>';
    }

    return `
      <div class="score-items">
        ${breakdown.map(item => `
          <div class="score-item">
            <span class="score-description">${item.description}</span>
            <span class="score-value ${item.value >= 0 ? 'positive' : 'negative'}">${item.value >= 0 ? '+' : ''}${item.value}</span>
          </div>
        `).join('')}
        <div class="score-total">
          <span class="score-description"><strong>Total</strong></span>
          <span class="score-value"><strong>${breakdown.reduce((sum, item) => sum + item.value, 0)}</strong></span>
        </div>
      </div>
    `;
  }

  // Calculer le breakdown détaillé du score
  getScoreBreakdown(aspectsArray, summaryType) {
    const breakdown = [];
    const aspectWeights = this.predictionWeights[summaryType] || {};

    if (Array.isArray(aspectsArray)) {
      // Grouper les aspects par type
      const aspectGroups = {};
      aspectsArray.forEach(aspect => {
        const key = aspect.aspectType;
        if (!aspectGroups[key]) {
          aspectGroups[key] = [];
        }
        aspectGroups[key].push(aspect);
      });

      // Calculer le score pour chaque groupe
      Object.entries(aspectGroups).forEach(([aspectType, aspects]) => {
        const weight = aspectWeights[aspectType] || 0;
        const totalScore = weight * aspects.length;
        breakdown.push({
          description: `${aspects.length}x ${this.getAspectName(aspectType)}`,
          value: totalScore
        });
      });

      // Bonus pour planètes importantes
      let transitBonus = 0;
      let natalBonus = 0;

      aspectsArray.forEach(aspect => {
        if (['sun', 'moon', 'mars', 'jupiter'].includes(aspect.transitPlanet)) {
          transitBonus += 0.5;
        }
        if (['sun', 'moon', 'venus', 'mars'].includes(aspect.natalPlanet)) {
          natalBonus += 0.5;
        }
      });

      if (transitBonus > 0) {
        breakdown.push({
          description: 'Bonus planètes de transit importantes',
          value: transitBonus
        });
      }

      if (natalBonus > 0) {
        breakdown.push({
          description: 'Bonus planètes natales importantes',
          value: natalBonus
        });
      }
    }

    return breakdown;
  }

  // Obtenir le nom d'un aspect
  getAspectName(aspectType) {
    const names = {
      'conjunction': 'Conjonction',
      'sextile': 'Sextile',
      'square': 'Carré',
      'trine': 'Trigone',
      'opposition': 'Opposition'
    };
    return names[aspectType] || aspectType;
  }

  // Obtenir le nom d'une planète
  getPlanetName(planetKey) {
    const planetNames = {
      sun: 'Soleil', moon: 'Lune', mercury: 'Mercure', venus: 'Vénus',
      mars: 'Mars', jupiter: 'Jupiter', saturn: 'Saturne',
      uranus: 'Uranus', neptune: 'Neptune', pluto: 'Pluton'
    };
    return planetNames[planetKey] || planetKey;
  }

  // Utilitaires pour récupérer les paramètres de filtrage
  getSelectedAspectTypes(tabId) {
    const suffix = tabId === 'current-month' ? '' : `-${tabId.split('-')[0]}`;
    const checkboxes = document.querySelectorAll(`input[name="monthly-aspect-type${suffix}"]:checked`);
    return Array.from(checkboxes).map(cb => cb.value);
  }

  getSelectedOrb(tabId) {
    const suffix = tabId === 'current-month' ? '' : `-${tabId.split('-')[0]}`;
    const orbFilter = document.getElementById(`monthly-orb-filter${suffix}`);
    return orbFilter ? parseFloat(orbFilter.value) : 0.5;
  }
}

// Instance globale du système de prédictions
let monthlyPredictions;

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
  monthlyPredictions = new MonthlyPredictionsSystem();

  // Ajouter un bouton pour déclencher les prédictions
  addPredictionButton();
});

// Ajouter un bouton pour déclencher les prédictions manuellement
function addPredictionButton() {
  const nextMonthHeader = document.querySelector('#next-month .monthly-table-header');
  if (nextMonthHeader) {
    const predictBtn = document.createElement('button');
    predictBtn.id = 'generate-predictions-btn';
    predictBtn.className = 'monthly-apply-filter-btn';
    predictBtn.textContent = '🔮 Générer Prédictions';
    predictBtn.style.marginLeft = '10px';

    predictBtn.addEventListener('click', () => {
      monthlyPredictions.predictNextMonth();
    });

    // Ajouter le bouton après le bouton "Appliquer"
    const applyBtn = nextMonthHeader.querySelector('#monthly-apply-filter-next');
    if (applyBtn && applyBtn.parentNode) {
      applyBtn.parentNode.appendChild(predictBtn);
    }
  }
}

// Auto-génération des prédictions quand on change d'onglet vers le mois suivant
document.addEventListener('click', function(event) {
  if (event.target.matches('[data-tab="next-month"]')) {
    // Délai pour laisser le temps au tableau de se générer
    setTimeout(() => {
      if (monthlyPredictions) {
        monthlyPredictions.predictNextMonth();
      }
    }, 500);
  }
});
