/**
 * Simplified Sidereal Astrology Chrome Extension Side Panel
 * Only contains functions for the circular proportional zodiac
 */

document.addEventListener('DOMContentLoaded', function() {
  // DOM elements that exist in the simplified version
  const birthDateInput = document.getElementById('birth-date');
  const birthHourInput = document.getElementById('birth-hour');
  const birthMinuteInput = document.getElementById('birth-minute');
  const transitDateInput = document.getElementById('transit-date');
  const transitHourInput = document.getElementById('transit-hour');
  const transitMinuteInput = document.getElementById('transit-minute');
  const calculateBtn = document.getElementById('calculate-btn');
  const toggleTransitModeBtn = document.getElementById('toggle-transit-mode');
  const transitModeLabel = document.getElementById('transit-mode-label');
  const timeNavigationBox = document.getElementById('time-navigation-box');
  const resetToNowBtn = document.getElementById('reset-to-now-btn');
  const showLegendBtn = document.getElementById('show-legend-btn');
  const legendModal = document.getElementById('legend-modal');
  const closeLegendBtn = document.getElementById('close-legend');
  const show360ChartBtn = document.getElementById('show-360-chart-btn');
  const planetPositionNavigation = document.getElementById('planet-position-navigation');
  const selectedPlanetTitle = document.getElementById('selected-planet-title');
  const planetNavDescription = document.getElementById('planet-nav-description');
  const prevPositionBtn = document.getElementById('prev-position-btn');
  const nextPositionBtn = document.getElementById('next-position-btn');
  const planetNavDetails = document.getElementById('planet-nav-details');
  const currentPlanetPosition = document.getElementById('current-planet-position');

  // Set default birth date to 17/12/1991 7h27
  birthDateInput.value = '1991-12-17';
  birthHourInput.value = '7';
  birthMinuteInput.value = '27';

  // Transit mode: true = auto, false = manual
  let isAutoTransitMode = false;

  // Global variable to track monthly table mode
  let isMonthlyTable360Mode = false;

  // Variables for planet position navigation
  let selectedPlanet = null;
  let selectedPlanetPosition = null;

  // Set transit date to current date and time initially
  updateTransitToCurrentDateTime();

  // Initialize manual mode interface
  initializeManualMode();

  // Update transit date and time every second to keep it synchronized with PC time (only in auto mode)
  setInterval(function() {
    if (isAutoTransitMode) {
      updateTransitToCurrentDateTime();
      calculateChart();
    }
  }, 1000);

  // Add event listener for calculate button
  calculateBtn.addEventListener('click', function() {
    calculateChart();
  });

  // Add event listener for transit mode toggle button
  toggleTransitModeBtn.addEventListener('click', function() {
    toggleTransitMode();
  });

  // Function to update transit date and time to current date and time
  function updateTransitToCurrentDateTime() {
    const now = new Date();
    transitDateInput.value = now.toISOString().split('T')[0];
    transitHourInput.value = now.getHours();
    transitMinuteInput.value = now.getMinutes();

    // Update seconds field if it exists
    const transitSecondInput = document.getElementById('transit-second');
    if (transitSecondInput) {
      transitSecondInput.value = now.getSeconds();
    }
  }

  // Function to toggle between auto and manual transit mode
  function toggleTransitMode() {
    isAutoTransitMode = !isAutoTransitMode;

    const transitSecondInput = document.getElementById('transit-second');

    if (isAutoTransitMode) {
      // Switch to auto mode
      toggleTransitModeBtn.textContent = 'Mode Manuel';
      transitModeLabel.textContent = '(Auto-updated)';

      // Hide time navigation box
      if (timeNavigationBox) {
        timeNavigationBox.style.display = 'none';
      }

      // Disable inputs and update to current time
      transitDateInput.disabled = true;
      transitHourInput.disabled = true;
      transitMinuteInput.disabled = true;
      if (transitSecondInput) {
        transitSecondInput.disabled = true;
      }

      // Update to current time immediately
      updateTransitToCurrentDateTime();
      calculateChart();

    } else {
      // Switch to manual mode
      toggleTransitModeBtn.textContent = 'Mode Auto';
      transitModeLabel.textContent = '(Manuel)';

      // Show time navigation box
      if (timeNavigationBox) {
        timeNavigationBox.style.display = 'block';
      }

      // Enable inputs for manual editing
      transitDateInput.disabled = false;
      transitHourInput.disabled = false;
      transitMinuteInput.disabled = false;
      if (transitSecondInput) {
        transitSecondInput.disabled = false;
      }
    }

    // Update visual styling
    updateTransitFieldsVisualState();
  }

  // Function to update visual state of transit fields
  function updateTransitFieldsVisualState() {
    const transitSecondInput = document.getElementById('transit-second');
    const fields = [transitDateInput, transitHourInput, transitMinuteInput];
    if (transitSecondInput) fields.push(transitSecondInput);

    fields.forEach(field => {
      if (isAutoTransitMode) {
        field.classList.add('auto-updated-field');
        field.style.backgroundColor = '#f0f8ff';
        field.style.color = '#666';
      } else {
        field.classList.remove('auto-updated-field');
        field.style.backgroundColor = '';
        field.style.color = '';
      }
    });
  }

  // Function to initialize manual mode interface
  function initializeManualMode() {
    const transitSecondInput = document.getElementById('transit-second');

    // Set button text and label for manual mode
    toggleTransitModeBtn.textContent = 'Mode Auto';
    transitModeLabel.textContent = '(Manuel)';

    // Show time navigation box
    if (timeNavigationBox) {
      timeNavigationBox.style.display = 'block';
    }

    // Enable inputs for manual editing
    transitDateInput.disabled = false;
    transitHourInput.disabled = false;
    transitMinuteInput.disabled = false;
    if (transitSecondInput) {
      transitSecondInput.disabled = false;
    }

    // Update visual styling
    updateTransitFieldsVisualState();
  }

  // Load saved dates and times from storage
  loadBirthDataFromStorage();

  // Function to load birth data from chrome.storage.sync
  async function loadBirthDataFromStorage() {
    try {
      const birthData = await StorageMigration.loadBirthData();

      birthDateInput.value = birthData.birthDate;
      birthHourInput.value = birthData.birthHour;
      birthMinuteInput.value = birthData.birthMinute;

      // Always use current date and time for transit
      updateTransitToCurrentDateTime();

      // Initialize visual state
      updateTransitFieldsVisualState();

      // Calculate chart with saved birth date/time and current transit date/time
      calculateChart();
    } catch (error) {
      console.error('Erreur lors du chargement des données de naissance:', error);
      // Fallback to defaults
      birthDateInput.value = '1991-12-17';
      birthHourInput.value = '7';
      birthMinuteInput.value = '27';
      updateTransitToCurrentDateTime();
      updateTransitFieldsVisualState();
      calculateChart();
    }
  }

  // Event listeners
  calculateBtn.addEventListener('click', function() {
    calculateChart();

    // Save birth date and time to storage
    saveBirthDataToStorage();
  });

  // Function to save birth data to chrome.storage.sync
  async function saveBirthDataToStorage() {
    try {
      await StorageMigration.saveBirthData(
        birthDateInput.value,
        parseInt(birthHourInput.value),
        parseInt(birthMinuteInput.value)
      );
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données de naissance:', error);
      // Fallback to localStorage
      chrome.storage.local.set({
        birthDate: birthDateInput.value,
        birthHour: parseInt(birthHourInput.value),
        birthMinute: parseInt(birthMinuteInput.value)
      });
    }
  }

  // Add input validation for hour and minute fields
  birthHourInput.addEventListener('change', function() {
    validateTimeInput(birthHourInput, 0, 23);
  });

  birthMinuteInput.addEventListener('change', function() {
    validateTimeInput(birthMinuteInput, 0, 59);
  });

  transitHourInput.addEventListener('change', function() {
    validateTimeInput(transitHourInput, 0, 23);
    if (!isAutoTransitMode) {
      calculateChart();
    }
  });

  transitMinuteInput.addEventListener('change', function() {
    validateTimeInput(transitMinuteInput, 0, 59);
    if (!isAutoTransitMode) {
      calculateChart();
    }
  });

  // Add event listeners for manual transit changes
  transitDateInput.addEventListener('change', function() {
    if (!isAutoTransitMode) {
      calculateChart();
      // Update day comments display
      if (window.updateDayCommentsDisplay) {
        window.updateDayCommentsDisplay().catch(console.error);
      }
    }
  });

  const transitSecondInput = document.getElementById('transit-second');
  if (transitSecondInput) {
    transitSecondInput.addEventListener('change', function() {
      if (!isAutoTransitMode) {
        calculateChart();
      }
    });
  }

  // Resize handler for the canvas
  window.addEventListener('resize', function() {
    calculateChart();
  });

  // Calculate and display the chart
  function calculateChart() {
    try {
      // Create date objects with time
      const birthDate = new Date(birthDateInput.value);
      birthDate.setHours(
        parseInt(birthHourInput.value) || 0,
        parseInt(birthMinuteInput.value) || 0,
        0
      );

      // Create transit date object
      let transitDate;
      if (isAutoTransitMode) {
        // In auto mode, use current time
        transitDate = new Date();
      } else {
        // In manual mode, use the values from the input fields
        transitDate = new Date(transitDateInput.value);
        transitDate.setHours(
          parseInt(transitHourInput.value) || 0,
          parseInt(transitMinuteInput.value) || 0,
          0
        );
      }

      // Get seconds from the transit second input if it exists
      const transitSecondInput = document.getElementById('transit-second');
      const seconds = transitSecondInput ? parseInt(transitSecondInput.value) || 0 : 0;

      // Set seconds in the transit date
      transitDate.setSeconds(seconds);

      // Calculate planetary positions
      const birthPositions = calculatePlanetaryPositions(birthDate);
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Store positions globally for other functions (before drawing)
      window.birthPositions = birthPositions;
      window.transitPositions = transitPositions;
      window.lastBirthPositions = birthPositions;
      window.lastTransitPositions = transitPositions;

      // Check and apply astrological rules if the system is available
      if (window.astroRules && typeof window.astroRules.checkActiveRules === 'function') {
        window.astroRules.checkActiveRules();
      }

      // Draw circular proportional zodiac
      drawCircularProportionalZodiac(birthPositions, transitPositions);

      // Calculate and display proportional aspects
      calculateAndDisplayProportionalAspects(birthPositions, transitPositions);

      // Apply astrological rules if the system is initialized
      if (window.astroRules) {
        // Convert transit positions to the format expected by the rules system
        const transitPositionsForRules = {};
        Object.keys(transitPositions).forEach(planetKey => {
          const planetData = transitPositions[planetKey];
          if (planetData) {
            // Calculate absolute longitude (0-360°)
            const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
            const absoluteLongitude = signIndex * 30 + parseFloat(planetData.degree);
            transitPositionsForRules[planetKey] = absoluteLongitude;
          }
        });

        // Update the rules system with current transit positions
        window.astroRules.onChartUpdate(transitPositionsForRules);
      }

      console.log('Chart calculated successfully');
    } catch (error) {
      console.error('Error calculating chart:', error);
    }
  }

  // Input validation function
  function validateTimeInput(input, min, max) {
    const value = parseInt(input.value);
    if (isNaN(value) || value < min || value > max) {
      input.value = min;
    }
  }

  // Initialize proportional aspects filter
  initProportionalAspectsFilter();

  // Initialize interpretations button
  initInterpretationsButton();

  // Initialize time navigation
  initTimeNavigation();

  // Initialize legend modal
  initLegendModal();

  // Initialize 360° chart modal
  init360ChartModal();

  // Initialize planet position navigation
  initPlanetPositionNavigation();

  // Initialize monthly aspects table
  initMonthlyAspectsTable();

  // Initialize daily aspects modal
  initDailyAspectsModal();

  // Initialize mini calendar
  initMiniCalendar();

  // Initialize day color customization
  initDayColorCustomization();

  // Initialize day comments display
  updateDayCommentsDisplay().catch(console.error);

  // Initialize export/import functionality
  initExportImportFunctionality();

  // Fonction pour gérer les filtres des aspects proportionnels
  function initProportionalAspectsFilter() {
    const orbFilter = document.getElementById('proportional-orb-filter');
    const orbValue = document.getElementById('proportional-orb-value');
    const applyFilterBtn = document.getElementById('proportional-apply-filter');
    const aspectTypeCheckboxes = document.querySelectorAll('input[name="proportional-aspect-type"]');

    if (!orbFilter || !orbValue || !applyFilterBtn) return;

    // Mettre à jour l'affichage de la valeur de l'orb
    orbFilter.addEventListener('input', function() {
      orbValue.textContent = this.value + '°';
    });

    // Appliquer les filtres
    applyFilterBtn.addEventListener('click', function() {
      const orbVal = parseFloat(orbFilter.value);

      // Récupérer les types d'aspects sélectionnés
      const selectedTypes = [];
      aspectTypeCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
          selectedTypes.push(checkbox.value);
        }
      });

      // Récupérer les positions actuelles
      const birthDate = new Date(birthDateInput.value);
      birthDate.setHours(
        parseInt(birthHourInput.value) || 0,
        parseInt(birthMinuteInput.value) || 0,
        0
      );

      // Create transit date object based on current mode
      let transitDate;
      if (isAutoTransitMode) {
        transitDate = new Date();
      } else {
        transitDate = new Date(transitDateInput.value);
        transitDate.setHours(
          parseInt(transitHourInput.value) || 0,
          parseInt(transitMinuteInput.value) || 0,
          0
        );
        const transitSecondInput = document.getElementById('transit-second');
        const seconds = transitSecondInput ? parseInt(transitSecondInput.value) || 0 : 0;
        transitDate.setSeconds(seconds);
      }

      const birthPositions = calculatePlanetaryPositions(birthDate);
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Calculer et afficher les aspects avec les nouveaux filtres
      const aspects = calculateProportionalAspects(transitPositions, birthPositions, orbVal, selectedTypes);

      // Stocker les aspects globalement pour les interprétations
      window.currentAspects = aspects;

      // Trouver le bon conteneur pour les aspects
      const aspectsContainer = document.querySelector('#proportional-zodiac-aspects .zodiac-aspects-list') ||
                              document.getElementById('proportional-zodiac-aspects');

      if (aspectsContainer) {
        displayZodiacAspects(aspects, aspectsContainer.id);
      }
    });
  }

  // Fonction pour calculer et afficher les aspects proportionnels
  function calculateAndDisplayProportionalAspects(birthPositions, transitPositions) {
    // Valeurs par défaut
    const orbValue = 1.0;
    const selectedAspectTypes = ['conjunction', 'opposition'];

    // Calculer les aspects
    const aspects = calculateProportionalAspects(transitPositions, birthPositions, orbValue, selectedAspectTypes);

    // Stocker les aspects globalement pour les interprétations
    window.currentAspects = aspects;

    // Afficher les aspects
    displayZodiacAspects(aspects, 'proportional-zodiac-aspects');
  }

  // Fonction pour calculer les aspects en mode proportionnel 30°
  function calculateProportionalAspects(transitPositions, birthPositions, orbValue, selectedAspectTypes) {
    const aspects = [];

    // Définir les aspects proportionnels (360° → 30°)
    const proportionalAspectTypes = [
      { name: 'conjunction', angle: 0, originalAngle: 0, orb: 1.0 },
      { name: 'victoire-martial', angle: 1.33, originalAngle: 16, orb: 1.0 },
      { name: 'trigone-2', angle: 3.33, originalAngle: 40, orb: 1.0 },
      { name: 'sextile', angle: 5, originalAngle: 60, orb: 1.0 },
      { name: 'square', angle: 7.5, originalAngle: 90, orb: 1.0 },
      { name: 'trine', angle: 10, originalAngle: 120, orb: 0.5 },
      { name: 'trigone-3', angle: 13.5, originalAngle: 162, orb: 1.0 },
      { name: 'opposition', angle: 15, originalAngle: 180, orb: 1.0 }
    ];

    // Filtrer les types d'aspects sélectionnés
    const typesToCheck = proportionalAspectTypes.filter(type =>
      selectedAspectTypes.includes(type.name)
    );

    // Planètes à vérifier
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    planets.forEach(transitPlanet => {
      planets.forEach(natalPlanet => {
        // Calculer les positions proportionnelles (0-30°)
        const transitPos = getProportionalPosition(transitPositions[transitPlanet]);
        const natalPos = getProportionalPosition(birthPositions[natalPlanet]);

        // Calculer la séparation en mode proportionnel
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 15) separation = 30 - separation; // Gérer le wrap-around à 30°

        // Vérifier chaque type d'aspect proportionnel
        typesToCheck.forEach(aspectType => {
          const aspectAngle = aspectType.angle;
          const orbDifference = Math.abs(separation - aspectAngle);
          const aspectOrbValue = aspectType.orb || orbValue; // Use specific orb or default

          // Vérifier si l'aspect est dans l'orb
          if (orbDifference <= aspectOrbValue) {
            aspects.push({
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType.name,
              aspectAngle: aspectAngle,
              originalAngle: aspectType.originalAngle,
              orb: orbDifference.toFixed(1),
              transitPosition: transitPos.toFixed(1),
              natalPosition: natalPos.toFixed(1),
              separation: separation.toFixed(1)
            });
          }
        });
      });
    });

    // Trier les aspects par orb (plus précis en premier)
    aspects.sort((a, b) => parseFloat(a.orb) - parseFloat(b.orb));

    return aspects;
  }

  // Fonction pour calculer les aspects en mode 360° pour le tableau mensuel
  function calculate360DegreeAspects(transitPositions, birthPositions, orbValue, selectedAspectTypes) {
    const aspects = [];

    // Définir les aspects 360° avec leurs orbs spécifiques
    const aspect360Types = [
      { name: 'conjunction', angle: 0, orb: 2.0 },
      { name: 'victoire-martial', angle: 16, orb: 2.0 },
      { name: 'trigone-2', angle: 40, orb: 2.0 },
      { name: 'sextile', angle: 60, orb: 2.0 },
      { name: 'square', angle: 90, orb: 2.0 },
      { name: 'trine', angle: 120, orb: 1.0 },
      { name: 'trigone-3', angle: 162, orb: 2.0 },
      { name: 'opposition', angle: 180, orb: 2.0 }
    ];

    // Filtrer les aspects selon la sélection de l'utilisateur
    const typesToCheck = selectedAspectTypes && selectedAspectTypes.length > 0
      ? aspect360Types.filter(type => selectedAspectTypes.includes(type.name))
      : aspect360Types;

    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    planets.forEach(transitPlanet => {
      if (!transitPositions[transitPlanet]) return;

      planets.forEach(natalPlanet => {
        if (!birthPositions[natalPlanet]) return;

        // Calculer les positions absolues (0-360°)
        const transitPos = getAbsolutePosition360(transitPositions[transitPlanet]);
        const natalPos = getAbsolutePosition360(birthPositions[natalPlanet]);

        // Calculer la séparation angulaire
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 180) separation = 360 - separation;

        // Vérifier chaque type d'aspect 360°
        typesToCheck.forEach(aspectType => {
          const aspectAngle = aspectType.angle;
          const orbDifference = Math.abs(separation - aspectAngle);
          const aspectOrbValue = aspectType.orb || 2.0; // Use specific orb or default

          // Vérifier si l'aspect est dans l'orb
          if (orbDifference <= aspectOrbValue) {
            const orb = separation - aspectAngle;

            aspects.push({
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType.name,
              orb: orb.toFixed(1),
              transitPosition: transitPos.toFixed(1),
              natalPosition: natalPos.toFixed(1),
              separation: separation.toFixed(1)
            });
          }
        });
      });
    });

    // Trier les aspects par orb (plus précis en premier)
    aspects.sort((a, b) => Math.abs(parseFloat(a.orb)) - Math.abs(parseFloat(b.orb)));

    return aspects;
  }

  // Fonction pour obtenir la position absolue en mode 360°
  function getAbsolutePosition360(planetData) {
    if (typeof planetData === 'number') {
      return planetData; // Déjà en position absolue
    }

    if (planetData.sign && planetData.degree !== undefined) {
      const signs = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                    'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];
      const signIndex = signs.indexOf(planetData.sign);
      return signIndex * 30 + parseFloat(planetData.degree);
    }

    return 0;
  }

  // Fonction pour obtenir la position proportionnelle (0-30°)
  function getProportionalPosition(planetData) {
    if (!planetData) return 0;

    // En mode proportionnel, on utilise seulement la position dans le signe (0-30°)
    // Peu importe le signe, on prend juste les degrés dans ce signe
    return parseFloat(planetData.degree);
  }

  // Fonction pour afficher les aspects dans une section dédiée
  function displayZodiacAspects(aspects, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Vider le conteneur
    container.innerHTML = '';

    // Si aucun aspect n'est trouvé
    if (aspects.length === 0) {
      container.innerHTML = '<div class="empty-aspects-message">Aucun aspect trouvé avec l\'orb actuel.</div>';
      return;
    }

    // Trier les aspects par orb (du plus précis au moins précis)
    const sortedAspects = [...aspects].sort((a, b) => parseFloat(a.orb) - parseFloat(b.orb));

    // Limiter à 10 aspects maximum pour ne pas surcharger l'interface
    const limitedAspects = sortedAspects.slice(0, 10);

    // Créer les éléments d'aspect
    limitedAspects.forEach(aspect => {
      const aspectDiv = document.createElement('div');
      aspectDiv.className = 'zodiac-aspect-item';
      aspectDiv.style.display = 'flex';
      aspectDiv.style.justifyContent = 'space-between';
      aspectDiv.style.alignItems = 'center';
      aspectDiv.style.padding = '8px';
      aspectDiv.style.marginBottom = '5px';
      aspectDiv.style.backgroundColor = '#f9f9f9';
      aspectDiv.style.borderRadius = '4px';

      // Créer l'élément d'information de l'aspect
      const aspectInfo = document.createElement('div');
      aspectInfo.className = 'zodiac-aspect-info';
      aspectInfo.style.flex = '1';

      // Planètes impliquées
      const aspectPlanets = document.createElement('div');
      aspectPlanets.className = 'zodiac-aspect-planets';
      aspectPlanets.style.fontWeight = 'bold';
      aspectPlanets.style.marginBottom = '2px';

      // Obtenir les noms français des planètes
      const transitPlanetName = getPlanetFrenchName(aspect.transitPlanet);
      const natalPlanetName = getPlanetFrenchName(aspect.natalPlanet);
      const aspectTypeName = getAspectFrenchName(aspect.aspectType);

      aspectPlanets.textContent = `${transitPlanetName} ${aspectTypeName} ${natalPlanetName}`;

      // Détails de l'aspect
      const aspectDetails = document.createElement('div');
      aspectDetails.className = 'zodiac-aspect-details';
      aspectDetails.style.fontSize = '12px';
      aspectDetails.style.color = '#666';

      if (aspect.transitSign && aspect.natalSign) {
        // Format Standard
        aspectDetails.textContent = `${aspect.transitSign} ${aspect.transitDegree}° - ${aspect.natalSign} ${aspect.natalDegree}° (Orb: ${aspect.orb}°)`;
      } else {
        // Format Nakshatra ou Proportionnel
        if (aspect.originalAngle !== undefined) {
          // Format Proportionnel - afficher les positions dans le système 30°
          aspectDetails.textContent = `${aspect.transitPosition}° - ${aspect.natalPosition}° en mode 30° (Orb: ${aspect.orb}°)`;
        } else {
          // Format Nakshatra
          aspectDetails.textContent = `${aspect.transitPosition}° - ${aspect.natalPosition}° (Orb: ${aspect.orb}°)`;
        }
      }

      // Ajouter les éléments à la div d'information
      aspectInfo.appendChild(aspectPlanets);
      aspectInfo.appendChild(aspectDetails);

      // Créer le bouton d'interprétation
      const interpretBtn = document.createElement('button');
      interpretBtn.textContent = 'Interprétation';
      interpretBtn.style.backgroundColor = '#4CAF50';
      interpretBtn.style.color = 'white';
      interpretBtn.style.border = 'none';
      interpretBtn.style.borderRadius = '4px';
      interpretBtn.style.padding = '4px 8px';
      interpretBtn.style.fontSize = '11px';
      interpretBtn.style.cursor = 'pointer';
      interpretBtn.style.marginLeft = '10px';

      // Ajouter l'événement click pour l'interprétation
      interpretBtn.addEventListener('click', function() {
        showAspectInterpretation(aspect);
      });

      // Ajouter l'information et le bouton à la div principale
      aspectDiv.appendChild(aspectInfo);
      aspectDiv.appendChild(interpretBtn);

      // Ajouter la div d'aspect au conteneur
      container.appendChild(aspectDiv);
    });
  }

  // Fonction pour obtenir le nom français d'une planète
  function getPlanetFrenchName(planetKey) {
    const planetNames = {
      'sun': 'Soleil',
      'moon': 'Lune',
      'mercury': 'Mercure',
      'venus': 'Vénus',
      'mars': 'Mars',
      'jupiter': 'Jupiter',
      'saturn': 'Saturne',
      'uranus': 'Uranus',
      'neptune': 'Neptune'
    };
    return planetNames[planetKey] || planetKey;
  }

  // Fonction pour obtenir le nom français d'un aspect
  function getAspectFrenchName(aspectType) {
    const aspectNames = {
      'conjunction': '☌',
      'victoire-martial': '⚔',
      'trigone-2': '△2',
      'sextile': '⚹',
      'square': '□',
      'trine': '△',
      'trigone-3': '△3',
      'opposition': '☍'
    };
    return aspectNames[aspectType] || aspectType;
  }

  // Draw the circular proportional zodiac
  function drawCircularProportionalZodiac(birthPositions, transitPositions) {
    const circularZodiacCanvas = document.getElementById('circular-zodiac-canvas');
    if (!circularZodiacCanvas) return;

    const ctx = circularZodiacCanvas.getContext('2d');
    const width = circularZodiacCanvas.width = circularZodiacCanvas.offsetWidth;
    const height = circularZodiacCanvas.height = 500;
    const centerX = width / 2;
    const centerY = height / 2; // Centré verticalement
    const radius = Math.max(10, Math.min(centerX, centerY) - 30);

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Définir les rayons pour les différents cercles
    const outerRadius = radius;           // Cercle extérieur pour les transits
    const innerRadius = radius * 0.65;    // Cercle intérieur pour les nataux
    const middleRadius = radius * 0.82;   // Cercle moyen pour les divisions

    // Draw outer circle (transits)
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#4A90E2';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw inner circle (natal)
    ctx.beginPath();
    ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw 30° divisions (12 divisions for proportional zodiac) with sector effects
    for (let i = 0; i < 12; i++) {
      const angle = (i * 30 - 90) * Math.PI / 180;
      const nextAngle = ((i + 1) * 30 - 90) * Math.PI / 180;
      const sector = i + 1; // Sectors are numbered 1-12

      // Check for sector effects
      let sectorColor = null;
      let sectorHighlight = null;

      // Check for test effects
      if (window.testRuleEffects && window.testRuleSector === sector) {
        if (window.testRuleEffects.sectorColor) {
          sectorColor = window.testRuleEffects.sectorColor.color;
        }
        if (window.testRuleEffects.sectorHighlight) {
          sectorHighlight = window.testRuleEffects.sectorHighlight.style;
        }
      }

      // Check for active rule effects
      if (window.activeRuleEffects) {
        window.activeRuleEffects.forEach((ruleData, key) => {
          if (ruleData.sector === sector) {
            if (ruleData.effects.sectorColor) {
              sectorColor = ruleData.effects.sectorColor.color;
            }
            if (ruleData.effects.sectorHighlight) {
              sectorHighlight = ruleData.effects.sectorHighlight.style;
            }
          }
        });
      }

      // Draw sector background if color effect is active
      if (sectorColor) {
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, outerRadius, angle, nextAngle);
        ctx.closePath();
        ctx.fillStyle = sectorColor;
        ctx.globalAlpha = 0.3; // Semi-transparent
        ctx.fill();
        ctx.globalAlpha = 1.0; // Reset alpha
      }

      // Apply sector highlight effects
      if (sectorHighlight) {
        ctx.save();
        switch (sectorHighlight) {
          case 'glow':
            ctx.shadowColor = '#6f42c1';
            ctx.shadowBlur = 10;
            break;
          case 'border':
            ctx.lineWidth = 4;
            ctx.strokeStyle = '#6f42c1';
            break;
          case 'pulse':
            // Pulse effect will be handled by CSS animation
            break;
        }
      }

      // Draw division lines
      ctx.beginPath();
      ctx.moveTo(
        centerX + innerRadius * Math.cos(angle),
        centerY + innerRadius * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + outerRadius * Math.cos(angle),
        centerY + outerRadius * Math.sin(angle)
      );
      ctx.strokeStyle = sectorHighlight === 'border' ? '#6f42c1' : '#333';
      ctx.lineWidth = sectorHighlight === 'border' ? 4 : 2;
      ctx.stroke();

      // Reset effects
      if (sectorHighlight) {
        ctx.restore();
      }

      // Add degree markers at each 30° position (representing 2.5° increments in the 30° system)
      const degreeValue = (i * 2.5).toFixed(1); // 12 divisions = 30°/12 = 2.5° each

      // Calculer l'angle au début de la division (pas au milieu)
      const startAngle = (i * 30 - 90) * Math.PI / 180;
      const textRadius = outerRadius + 15; // Placer le texte à l'extérieur du cercle
      const textX = centerX + textRadius * Math.cos(startAngle);
      const textY = centerY + textRadius * Math.sin(startAngle);

      ctx.font = 'bold 9px Arial';
      ctx.fillStyle = '#666';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(degreeValue + '°', textX, textY);
    }

    // Draw all aspects between natal and transit planets (behind the planets)
    drawProportionalAspects(ctx, centerX, centerY, innerRadius * 0.85, outerRadius * 0.92, birthPositions, transitPositions);

    // Draw planets for birth chart (cercle intérieur)
    drawProportionalPlanets(ctx, centerX, centerY, innerRadius * 0.85, birthPositions, false);

    // Draw planets for transit chart (cercle extérieur)
    drawProportionalPlanets(ctx, centerX, centerY, outerRadius * 0.92, transitPositions, true);

    // Setup click handlers for transit planets
    setupTransitPlanetClickHandlers(circularZodiacCanvas, centerX, centerY, outerRadius * 0.92, transitPositions);

    // Setup click handlers for aspect lines
    setupAspectLineClickHandlers(circularZodiacCanvas);

    // Store the base image for hover effects (after a small delay to ensure drawing is complete)
    setTimeout(() => {
      const ctx = circularZodiacCanvas.getContext('2d');
      circularZodiacCanvas.baseImageData = ctx.getImageData(0, 0, circularZodiacCanvas.width, circularZodiacCanvas.height);
    }, 10);
  }

  // Function to draw planets in proportional mode
  function drawProportionalPlanets(ctx, centerX, centerY, radius, positions, isTransit) {
    const planets = [
      { key: 'sun', name: 'Sun', symbol: '☉', color: isTransit ? '#FF6B35' : '#FFA500' },
      { key: 'moon', name: 'Moon', symbol: '☽', color: isTransit ? '#87CEEB' : '#C0C0C0' },
      { key: 'mercury', name: 'Mercury', symbol: '☿', color: isTransit ? '#32CD32' : '#90EE90' },
      { key: 'venus', name: 'Venus', symbol: '♀', color: isTransit ? '#FF1493' : '#FFB6C1' },
      { key: 'mars', name: 'Mars', symbol: '♂', color: isTransit ? '#DC143C' : '#FF6347' },
      { key: 'jupiter', name: 'Jupiter', symbol: '♃', color: isTransit ? '#4169E1' : '#87CEFA' },
      { key: 'saturn', name: 'Saturn', symbol: '♄', color: isTransit ? '#8B4513' : '#DEB887' },
      { key: 'uranus', name: 'Uranus', symbol: '♅', color: isTransit ? '#00CED1' : '#AFEEEE' },
      { key: 'neptune', name: 'Neptune', symbol: '♆', color: isTransit ? '#4B0082' : '#9370DB' }
    ];

    // Prepare planets with their positions for collision detection
    const planetsWithPositions = [];
    planets.forEach(planet => {
      const planetData = positions[planet.key];
      if (!planetData) return;

      // Calculer la position proportionnelle (0-30°)
      const proportionalPosition = getProportionalPosition(planetData);

      planetsWithPositions.push({
        ...planet,
        proportionalPosition: proportionalPosition,
        planetData: planetData
      });
    });

    // Sort planets by position for easier collision detection
    planetsWithPositions.sort((a, b) => a.proportionalPosition - b.proportionalPosition);

    // Group close planets (within 0.2°)
    const planetGroups = [];
    let currentGroup = [];

    for (let i = 0; i < planetsWithPositions.length; i++) {
      const planet = planetsWithPositions[i];

      if (currentGroup.length === 0) {
        currentGroup.push(planet);
      } else {
        const lastPlanet = currentGroup[currentGroup.length - 1];
        const distance = Math.abs(planet.proportionalPosition - lastPlanet.proportionalPosition);

        // If planets are very close (< 0.2°), add to current group
        if (distance < 0.2) {
          currentGroup.push(planet);
        } else {
          // Start new group
          planetGroups.push([...currentGroup]);
          currentGroup = [planet];
        }
      }
    }

    // Add the last group
    if (currentGroup.length > 0) {
      planetGroups.push(currentGroup);
    }

    // Draw each group
    planetGroups.forEach(group => {
      if (group.length === 1) {
        // Single planet - draw normally
        const planet = group[0];
        drawSinglePlanet(ctx, centerX, centerY, radius, planet, isTransit);
      } else {
        // Multiple close planets - arrange vertically
        drawPlanetGroup(ctx, centerX, centerY, radius, group, isTransit);
      }
    });
  }

  // Function to draw a single planet
  function drawSinglePlanet(ctx, centerX, centerY, radius, planet, isTransit) {
    // Convertir en angle pour le dessin (0° est à 3h, rotation anti-horaire)
    // 30° du zodiaque = 360° du cercle, donc multiplier par 12
    const angle = ((planet.proportionalPosition * 12) - 90) * Math.PI / 180;

    // Calculer la position sur le cercle
    const planetX = centerX + radius * Math.cos(angle);
    const planetY = centerY + radius * Math.sin(angle);

    drawPlanetSymbol(ctx, planetX, planetY, planet, isTransit);
  }

  // Function to draw a group of close planets aligned radially toward center
  function drawPlanetGroup(ctx, centerX, centerY, radius, group, isTransit) {
    // Use the average position for the group
    const avgPosition = group.reduce((sum, p) => sum + p.proportionalPosition, 0) / group.length;
    const angle = ((avgPosition * 12) - 90) * Math.PI / 180;

    // Calculate the direction vector from center to the base position
    const directionX = Math.cos(angle);
    const directionY = Math.sin(angle);

    // Spacing between planets along the radial line
    const radialSpacing = 20;
    const totalLength = (group.length - 1) * radialSpacing;

    // Start position: move inward from the circle by half the total length
    const startRadius = radius - totalLength / 2;

    // Draw each planet in the group along the radial line
    group.forEach((planet, index) => {
      const currentRadius = startRadius + index * radialSpacing;
      const planetX = centerX + currentRadius * directionX;
      const planetY = centerY + currentRadius * directionY;

      drawPlanetSymbol(ctx, planetX, planetY, planet, isTransit);
    });
  }

  // Function to draw planet symbol with effects
  function drawPlanetSymbol(ctx, planetX, planetY, planet, isTransit) {
    // Check for active rule effects
    let planetScale = 1;
    let planetGlow = null;
    let planetColor = planet.color;

    // Check for test effects
    if (window.testRuleEffects && window.testRulePlanet === planet.key && isTransit) {
      if (window.testRuleEffects.planetScale) {
        planetScale = window.testRuleEffects.planetScale.factor;
      }
      if (window.testRuleEffects.planetGlow) {
        planetGlow = window.testRuleEffects.planetGlow.color;
      }
    }

    // Check for active rule effects
    if (window.activeRuleEffects && isTransit) {
      window.activeRuleEffects.forEach((ruleData, key) => {
        if (ruleData.planet === planet.key) {
          if (ruleData.effects.planetScale) {
            planetScale = ruleData.effects.planetScale.factor;
          }
          if (ruleData.effects.planetGlow) {
            planetGlow = ruleData.effects.planetGlow.color;
          }
        }
      });
    }

    // Draw white background circle to make planet visible over aspect lines
    ctx.beginPath();
    ctx.arc(planetX, planetY, 12 * planetScale, 0, 2 * Math.PI);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fill();

    // Apply planet glow effect
    if (planetGlow) {
      ctx.shadowColor = planetGlow;
      ctx.shadowBlur = 15;
    }

    // Draw planet symbol with scaling
    ctx.save();
    ctx.translate(planetX, planetY);
    ctx.scale(planetScale, planetScale);
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = planetColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(planet.symbol, 0, 0);
    ctx.restore();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;

    // Draw small circle border with scaling
    ctx.beginPath();
    ctx.arc(planetX, planetY, 10 * planetScale, 0, 2 * Math.PI);
    ctx.strokeStyle = planetColor;
    ctx.lineWidth = 1;
    ctx.stroke();
  }

  // Function to draw all aspects between natal and transit planets
  function drawProportionalAspects(ctx, centerX, centerY, natalRadius, transitRadius, birthPositions, transitPositions) {
    const orbValue = 1.0; // Orb par défaut
    const selectedAspectTypes = ['conjunction', 'opposition']; // Types d'aspects à afficher

    // Clear previous aspect lines data
    if (!window.aspectLines) {
      window.aspectLines = [];
    }
    window.aspectLines = [];

    // Définir les aspects proportionnels (360° → 30°)
    const proportionalAspectTypes = [
      { name: 'conjunction', angle: 0, originalAngle: 0, color: '#FF0000', lineWidth: 2, orb: 1.0 },
      { name: 'victoire-martial', angle: 1.33, originalAngle: 16, color: '#FF8C00', lineWidth: 1, orb: 1.0 },
      { name: 'trigone-2', angle: 3.33, originalAngle: 40, color: '#32CD32', lineWidth: 1, orb: 1.0 },
      { name: 'sextile', angle: 5, originalAngle: 60, color: '#00AA00', lineWidth: 1, orb: 1.0 },
      { name: 'square', angle: 7.5, originalAngle: 90, color: '#FF6600', lineWidth: 1, orb: 1.0 },
      { name: 'trine', angle: 10, originalAngle: 120, color: '#0066FF', lineWidth: 1, orb: 0.5 },
      { name: 'trigone-3', angle: 13.5, originalAngle: 162, color: '#4169E1', lineWidth: 1, orb: 1.0 },
      { name: 'opposition', angle: 15, originalAngle: 180, color: '#000000', lineWidth: 2, orb: 1.0 }
    ];

    // Filtrer les types d'aspects sélectionnés
    const typesToCheck = proportionalAspectTypes.filter(type =>
      selectedAspectTypes.includes(type.name)
    );

    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    planets.forEach(transitPlanet => {
      planets.forEach(natalPlanet => {
        const transitData = transitPositions[transitPlanet];
        const natalData = birthPositions[natalPlanet];

        if (!transitData || !natalData) return;

        // Calculer les positions proportionnelles (0-30°)
        const transitPos = getProportionalPosition(transitData);
        const natalPos = getProportionalPosition(natalData);

        // Calculer la séparation en mode proportionnel
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 15) separation = 30 - separation; // Gérer le wrap-around à 30°

        // Vérifier chaque type d'aspect proportionnel
        typesToCheck.forEach(aspectType => {
          const aspectAngle = aspectType.angle;
          const orbDifference = Math.abs(separation - aspectAngle);
          const aspectOrbValue = aspectType.orb || orbValue; // Use specific orb or default

          // Vérifier si l'aspect est dans l'orb
          if (orbDifference <= aspectOrbValue) {
            // Calculer les positions sur le cercle pour les deux planètes
            const transitAngle = ((transitPos * 12) - 90) * Math.PI / 180;
            const natalAngle = ((natalPos * 12) - 90) * Math.PI / 180;

            // Distance from planet center to line connection point (to avoid overlapping with planet markers)
            const planetMarkerRadius = 12; // Radius of planet markers
            const connectionOffset = planetMarkerRadius + 3; // 3px additional spacing

            // Calculate connection points:
            // - Transit: at the edge of transit planet markers (closer to center)
            // - Natal: at the edge of the inner circle (to avoid crossing natal planets)
            const transitConnectionRadius = transitRadius - connectionOffset;
            const natalConnectionRadius = natalRadius - connectionOffset; // Changed from + to - to stop at inner circle edge

            const transitX = centerX + transitConnectionRadius * Math.cos(transitAngle);
            const transitY = centerY + transitConnectionRadius * Math.sin(transitAngle);

            const natalX = centerX + natalConnectionRadius * Math.cos(natalAngle);
            const natalY = centerY + natalConnectionRadius * Math.sin(natalAngle);

            // Store aspect line data for click detection
            window.aspectLines.push({
              x1: natalX,
              y1: natalY,
              x2: transitX,
              y2: transitY,
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType.name,
              orb: orbDifference.toFixed(1),
              transitPosition: transitPos.toFixed(1),
              natalPosition: natalPos.toFixed(1),
              separation: separation.toFixed(1),
              originalAngle: aspectType.originalAngle
            });

            // Dessiner la ligne d'aspect
            ctx.beginPath();
            ctx.moveTo(natalX, natalY);
            ctx.lineTo(transitX, transitY);
            ctx.strokeStyle = aspectType.color;
            ctx.lineWidth = aspectType.lineWidth;
            ctx.stroke();
          }
        });
      });
    });
  }

  // Function to show interpretation for a specific aspect
  function showAspectInterpretation(aspect) {
    const modal = document.getElementById('interpretations-modal');
    const content = document.getElementById('interpretations-content');

    if (!modal || !content) return;

    // Get planet names and aspect type
    const transitPlanet = getPlanetFrenchName(aspect.transitPlanet);
    const natalPlanet = getPlanetFrenchName(aspect.natalPlanet);
    const aspectType = getAspectFrenchName(aspect.aspectType);

    // Get specific interpretation
    const interpretation = getSpecificInterpretation(aspect.transitPlanet, aspect.natalPlanet, aspect.aspectType);

    // Use default message if no interpretation is found
    const displayInterpretation = interpretation || "Pas d'interprétation trouvée pour cet aspect.";

    // Create unique key for this interpretation
    const interpretationKey = `${aspect.transitPlanet}_${aspect.aspectType}_${aspect.natalPlanet}`;

    // Create content for this specific aspect
    let htmlContent = `
      <div class="specific-aspect-interpretation">
        <h4>${transitPlanet} ${getAspectFrenchName(aspect.aspectType)} ${natalPlanet}</h4>
        <div class="aspect-details-box" style="background-color: #e8f4fd; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
          <strong>Type d'aspect:</strong> ${aspectType}<br>
          <strong>Orb:</strong> ${aspect.orb}°<br>
          <strong>Positions:</strong> ${aspect.transitPosition}° - ${aspect.natalPosition}° (mode 30°)
        </div>
        <div class="interpretation-content">
          <h5>Interprétation:</h5>
          <div id="interpretation-display" style="line-height: 1.6; color: #333; margin-bottom: 15px;">
            ${displayInterpretation}
          </div>
          <textarea id="interpretation-editor" style="display: none; width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-family: inherit; line-height: 1.6;">${displayInterpretation}</textarea>
          <div class="interpretation-edit-actions">
            <button id="edit-interpretation-btn" class="edit-btn">Éditer</button>
            <button id="save-interpretation-btn" class="save-btn" style="display: none;">Sauvegarder</button>
            <button id="cancel-edit-btn" class="cancel-btn" style="display: none;">Annuler</button>
          </div>
        </div>
      </div>
    `;

    content.innerHTML = htmlContent;

    // Add event listeners for edit functionality
    setupInterpretationEditListeners(interpretationKey, displayInterpretation);

    modal.style.display = 'block';
  }

  // Function to setup edit listeners for interpretation
  function setupInterpretationEditListeners(interpretationKey, originalInterpretation) {
    const editBtn = document.getElementById('edit-interpretation-btn');
    const saveBtn = document.getElementById('save-interpretation-btn');
    const cancelBtn = document.getElementById('cancel-edit-btn');
    const display = document.getElementById('interpretation-display');
    const editor = document.getElementById('interpretation-editor');

    if (!editBtn || !saveBtn || !cancelBtn || !display || !editor) return;

    // Edit button click
    editBtn.addEventListener('click', function() {
      display.style.display = 'none';
      editor.style.display = 'block';
      editBtn.style.display = 'none';
      saveBtn.style.display = 'inline-block';
      cancelBtn.style.display = 'inline-block';
      editor.focus();
    });

    // Cancel button click
    cancelBtn.addEventListener('click', function() {
      editor.value = originalInterpretation;
      display.style.display = 'block';
      editor.style.display = 'none';
      editBtn.style.display = 'inline-block';
      saveBtn.style.display = 'none';
      cancelBtn.style.display = 'none';
    });

    // Save button click
    saveBtn.addEventListener('click', function() {
      const newInterpretation = editor.value.trim();
      if (newInterpretation) {
        // Save to localStorage
        saveCustomInterpretation(interpretationKey, newInterpretation);

        // Update display
        display.innerHTML = newInterpretation;
        display.style.display = 'block';
        editor.style.display = 'none';
        editBtn.style.display = 'inline-block';
        saveBtn.style.display = 'none';
        cancelBtn.style.display = 'none';

        // Show success message
        showSaveMessage('Interprétation sauvegardée avec succès !');
      }
    });
  }

  // Function to save custom interpretation to localStorage
  function saveCustomInterpretation(key, interpretation) {
    try {
      let customInterpretations = JSON.parse(localStorage.getItem('customInterpretations') || '{}');
      customInterpretations[key] = interpretation;
      localStorage.setItem('customInterpretations', JSON.stringify(customInterpretations));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    }
  }

  // Function to get custom interpretation from localStorage
  function getCustomInterpretation(key) {
    try {
      const customInterpretations = JSON.parse(localStorage.getItem('customInterpretations') || '{}');
      return customInterpretations[key] || null;
    } catch (error) {
      console.error('Erreur lors de la récupération:', error);
      return null;
    }
  }

  // Function to show save message
  function showSaveMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    setTimeout(() => {
      document.body.removeChild(messageDiv);
    }, 3000);
  }

  // Function to get specific interpretation for an aspect
  function getSpecificInterpretation(transitPlanet, natalPlanet, aspectType) {
    // First check for custom interpretation
    const customKey = `${transitPlanet}_${aspectType}_${natalPlanet}`;
    const customInterpretation = getCustomInterpretation(customKey);
    if (customInterpretation) {
      return customInterpretation;
    }

    // Convert planet names to abbreviations used in the interpretation file
    const planetAbbreviations = {
      'sun': 'SU',
      'moon': 'MO',
      'mercury': 'ME',
      'venus': 'VE',
      'mars': 'MA',
      'jupiter': 'JU',
      'saturn': 'SA',
      'uranus': 'UR',
      'neptune': 'NE',
      'pluto': 'PL',
      'ascendant': 'AS',
      'midheaven': 'MC'
    };

    // Convert aspect names to abbreviations used in the interpretation file
    const aspectAbbreviations = {
      'conjunction': 'CNJ',
      'victoire-martial': 'VMA',
      'trigone-2': 'TR2',
      'sextile': 'SXT',
      'square': 'SQR',
      'trine': 'TRI',
      'trigone-3': 'TR3',
      'opposition': 'OPP'
    };

    // Get abbreviations
    const transitAbbr = planetAbbreviations[transitPlanet];
    const natalAbbr = planetAbbreviations[natalPlanet];
    const aspectAbbr = aspectAbbreviations[aspectType];

    if (!transitAbbr || !natalAbbr || !aspectAbbr) {
      return null; // Return null if no valid abbreviations
    }

    // Create the key in the format used in the interpretation file
    const interpretationKey = `[${transitAbbr}.${aspectAbbr}.${natalAbbr}]`;

    // Get interpretation from the database
    const interpretation = getInterpretationFromDatabase(interpretationKey);

    return interpretation; // Return the specific interpretation or null if not found
  }

  // Function to get interpretation from the database
  function getInterpretationFromDatabase(key) {
    // Database of interpretations from the French transit file
    const interpretationDatabase = {
      '[JU.CNJ.AS]': 'Vous bénéficierez d\'une orientation plus intérieure, vous détournant un peu du monde extérieur et permettant à de nouvelles idées et informations de s\'infiltrer. Un tournant professionnel, après lequel vous devriez lâcher un peu prise et vous attendre à une certaine régénération.',
      '[JU.OPP.AS]': 'Ceci marque l\'avènement d\'une bonne période en ce qui concerne la carrière et la vie sociale. Une période où vous avez payé votre dû est révolue ; de réelles réalisations sont maintenant possibles. Vous pourriez devoir sacrifier une partie de votre vie personnelle pour obtenir cette croissance extérieure.',
      '[JU.SQR.AS]': 'Une période où le travail et la famille occupent la majeure partie de votre temps. Vous pourriez constater que les amis et de nombreux intérêts personnels sont laissés de côté. Cela marque un moment où l\'ensemble de votre chemin de vie pourrait être redéfini et vous voir prendre une nouvelle direction professionnelle.',
      '[JU.SXT.AS]': 'Vous bénéficierez d\'une orientation plus intérieure, vous détournant un peu du monde extérieur et permettant à de nouvelles idées et informations de s\'infiltrer. Un tournant professionnel, après lequel vous devriez vous attendre à lâcher un peu prise et à subir une certaine régénération.',
      '[JU.TRI.AS]': 'Vous bénéficierez d\'une orientation plus intérieure, vous détournant un peu du monde extérieur et permettant à de nouvelles idées et informations de s\'infiltrer. Un tournant professionnel, après lequel vous devriez vous attendre à lâcher un peu prise et à subir une certaine régénération.',

      '[JU.CNJ.JU]': 'Une période de chance où les choses s\'ouvrent de manière très naturelle pour vous. Les situations sont presque sur mesure et il est facile de voir quel chemin emprunter. Les opportunités abondent, et vous pourriez vous retrouver à vouloir (et pouvoir) faire presque tout.',
      '[JU.OPP.JU]': 'Possiblement un tournant professionnel qui pourrait nécessiter une réflexion approfondie et un bon jugement de votre part. Le flux actuel des événements pourrait dicter un chemin qui va à l\'encontre de vos meilleurs talents et capacités. Ne vous sous-estimez pas. Tenez bon.',
      '[JU.SQR.JU]': 'Votre carrière pourrait être à un point plutôt délicat et sous forte pression. Il pourrait être difficile de trouver des solutions ou de prendre les bonnes décisions quant à la manière de procéder. L\'ensemble du processus pourrait être bloqué et lent. Vous pourriez devoir prendre votre mal en patience.',
      '[JU.SXT.JU]': 'Une période de chance où les choses s\'ouvrent de manière très naturelle pour vous. Les situations sont presque sur mesure, et il est facile de voir quel chemin emprunter. Les opportunités abondent, et vous pourriez vous retrouver à vouloir (et pouvoir) faire presque tout.',
      '[JU.TRI.JU]': 'Une période de chance où les choses s\'ouvrent de manière très naturelle pour vous. Les situations sont presque sur mesure, et il est facile de voir quel chemin emprunter. Les opportunités abondent, et vous pourriez vous retrouver à vouloir (et pouvoir) faire presque tout.',

      '[MA.CNJ.AS]': 'Vous pouvez vraiment communiquer et vous faire comprendre des autres en ce moment. Vous avez beaucoup d\'énergie et apparaissez comme assertif et dominant.',
      '[MA.OPP.AS]': 'Des problèmes avec des partenaires ou d\'autres personnes peuvent vous empêcher de vous montrer sous votre meilleur jour.',
      '[MA.SQR.AS]': 'Vous pourriez être sous forte pression, du moins dans la façon dont vous apparaissez aux autres. Tout n\'est pas en harmonie entre vos émotions et la façon dont vous vous présentez.',
      '[MA.SXT.AS]': 'Vous pouvez vraiment communiquer et vous faire comprendre des autres en ce moment. Vous avez beaucoup d\'énergie et apparaissez comme assertif et dominant.',
      '[MA.TRI.AS]': 'Vous pouvez vraiment communiquer et vous faire comprendre des autres en ce moment. Vous avez beaucoup d\'énergie et apparaissez comme assertif et dominant.',

      '[ME.CNJ.AS]': 'Vous devriez être capable de faire passer vos idées avec aisance. Les ingrédients pour une journée brillante et spirituelle. Vos compétences en communication sont à leur apogée.',
      '[ME.OPP.AS]': 'Vous vous trouvez orienté vers les autres en ce moment, avec beaucoup de pensées et d\'idées pour des partenariats, plutôt que de faire les choses seul. Vos amis pourraient vous trouver moins égocentrique que d\'habitude.',
      '[ME.SQR.AS]': 'Vous pourriez avoir quelques difficultés à parler ou à faire passer vos idées et pensées aux autres. Vous pourriez dire la mauvaise chose.',
      '[ME.SXT.AS]': 'Vous pouvez vraiment faire passer vos pensées et vos idées. Bonne communication.',
      '[ME.TRI.AS]': 'Vous pouvez vraiment faire passer vos pensées et vos idées. Bonne communication.',

      '[MO.CNJ.AS]': 'Communiquer (faire passer votre message aux autres) est à son apogée en ce moment. Votre timing devrait être parfait, et ceux qui vous entourent devraient vous trouver des plus spontanés et vivants.',
      '[MO.OPP.AS]': 'Vous vous sentez plus intérieur et plus enclin à partager du temps avec votre amoureux ou avec des amis proches. Peut-être pas un moment pour être très extraverti ou pour essayer de vous présenter au monde. Vous vous sentez réfléchi plutôt qu\'expressif en ce moment.',
      '[MO.SQR.AS]': 'Pas un bon moment pour essayer de faire passer vos idées aux autres. Vous pourriez vous sentir bloqué et incapable de vous exprimer, en particulier dans une situation de groupe.',
      '[MO.SXT.AS]': 'Vous pouvez faire preuve d\'une grande compréhension et sensibilité aux besoins des autres en ce moment et êtes en bonne position pour communiquer concernant les groupes et la société en général.',
      '[MO.TRI.AS]': 'Vous pouvez faire preuve d\'une grande compréhension et sensibilité aux besoins des autres en ce moment et êtes en bonne position pour communiquer concernant les groupes et la société en général.',

      // Ajout d'interprétations Soleil
      '[SU.CNJ.AS]': 'Une période formidable, où la chance et la bonne fortune vous entourent. Il vous est facile de prendre les bonnes décisions, de trouver le bon chemin et d\'avancer en ce qui concerne la carrière et le succès. Les problèmes de la vie semblent gérables et faciles à résoudre.',
      '[SU.OPP.AS]': 'Les opportunités apparaissent tout autour de vous mais semblent toujours défier votre statu quo. Vous devez décider si le risque vaut le changement qu\'il apportera à votre situation. Le choix évident peut aller à l\'encontre de vos meilleurs intérêts.',
      '[SU.SQR.AS]': 'Vous pourriez vivre une période un peu tendue alors que la direction de votre carrière (et le chemin vers le succès) s\'oppose à vos intérêts plus personnels. Cela pourrait être un véritable test pour vous. Faites attention à ne pas gâcher une opportunité de succès.',
      '[SU.SXT.AS]': 'Tout pourrait arriver en même temps, et ce ne sont que de bonnes nouvelles. La direction de votre carrière reçoit des encouragements, et les problèmes de la vie devraient trouver des solutions faciles. Vous pourriez bénéficier d\'une personne plus âgée ou en position d\'autorité.',
      '[SU.TRI.AS]': 'Tout pourrait arriver en même temps, et ce ne sont que de bonnes nouvelles. La direction de votre carrière reçoit des encouragements, et les problèmes de la vie devraient trouver des solutions faciles. Vous pourriez bénéficier d\'une personne plus âgée ou en position d\'autorité.',

      // Ajout d'interprétations Vénus
      '[VE.CNJ.AS]': 'Vous pourriez vous trouver plus qu\'un peu acquéreur, et votre appréciation actuelle pour à peu près tout pourrait vous amener à trop dépenser, à trop vous faire plaisir. Vous ferez des gains professionnels grâce à votre capacité à sentir la qualité et à choisir en conséquence.',
      '[VE.OPP.AS]': 'Ce qui est une décision de carrière claire (une solution facile) peut aller à l\'encontre de votre sens des valeurs, de ce que vous ressentez à ce sujet. Vous pourriez vous retrouver à devoir choisir entre votre carrière et votre sens du plaisir, les choses que vous aimez et appréciez.',
      '[VE.SQR.AS]': 'Les décisions de carrière qui mènent à un réel succès peuvent aller à l\'encontre de votre sens des valeurs. Vous pourriez ne pas être capable d\'apprécier cette voie ou ce chemin particulier vers le succès.',
      '[VE.SXT.AS]': 'Vous pourriez vous trouver plus qu\'un peu acquéreur, et votre appréciation actuelle pour à peu près tout pourrait vous amener à trop dépenser, à trop vous faire plaisir. Vous ferez des gains professionnels grâce à votre capacité à sentir la qualité.',
      '[VE.TRI.AS]': 'Vous pourriez vous trouver plus qu\'un peu acquéreur, et votre appréciation actuelle pour à peu près tout pourrait vous amener à trop dépenser, à trop vous faire plaisir. Vous ferez des gains professionnels grâce à votre capacité à sentir la qualité.',

      // Ajout d'interprétations Saturne
      '[SA.CNJ.AS]': 'Un moment pour se mettre au travail et consolider votre carrière. Vos capacités d\'organisation et votre sens des responsabilités seront ce qui vous guidera et s\'avérera fructueux. Votre carrière pourrait prendre une forme beaucoup plus déterminée et solide.',
      '[SA.OPP.AS]': 'Les opportunités peuvent aller à l\'encontre de votre propre sentiment de sécurité et de responsabilité. Les choix de carrière peuvent exiger que vous preniez un risque et négligiez le front domestique.',
      '[SA.SQR.AS]': 'Votre chemin ou votre carrière peut être en porte-à-faux avec votre propre sentiment de sécurité et vos responsabilités. Un compromis difficile à trouver. Vous ne pouvez pas gagner, donc des négociations s\'imposent.',
      '[SA.SXT.AS]': 'Un vrai moment pour se mettre au travail et consolider votre carrière. Vos capacités d\'organisation et votre sens des responsabilités seront ce qui vous guidera et s\'avérera fructueux.',
      '[SA.TRI.AS]': 'Un vrai moment pour se mettre au travail et consolider votre carrière. Vos capacités d\'organisation et votre sens des responsabilités seront ce qui vous guidera et s\'avérera fructueux.',

      // Ajout d'interprétations Jupiter avec Mars
      '[JU.CNJ.MA]': 'Les choses bougent, et votre carrière ou votre chemin dépend de votre propre ambition et dynamisme, qui sont forts actuellement. Capable d\'utiliser un bon sens pratique, vous pouvez sentir les tendances et faire les bons choix. Un moment pour avancer en passant à l\'action.',
      '[JU.OPP.MA]': 'Vous pourriez vous retrouver à devoir choisir entre ce qui est pratique (et peut-être profitable) et ce qui vous semble juste et approprié. Vos valeurs peuvent aller à l\'encontre de ce qui fonctionne.',
      '[JU.SQR.MA]': 'Vous pourriez avoir des difficultés à faire avancer les choses, en particulier votre carrière ou votre chemin de vie. Il peut y avoir un conflit entre vos valeurs et ce qui est pratique ou nécessaire.',
      '[JU.SXT.MA]': 'Vous pouvez faire des gains en étant pratique et en utilisant votre bon sens. Votre sens des valeurs et votre capacité à sentir les tendances vous aideront à faire les bons choix.',
      '[JU.TRI.MA]': 'Vous pouvez faire des gains en étant pratique et en utilisant votre bon sens. Votre sens des valeurs et votre capacité à sentir les tendances vous aideront à faire les bons choix.',

      // Ajout d'interprétations Jupiter avec Soleil
      '[JU.CNJ.SU]': 'Une période formidable, où la chance et la bonne fortune vous entourent. Il vous est facile de prendre les bonnes décisions, de trouver le bon chemin et d\'avancer en ce qui concerne la carrière et le succès. Les problèmes de la vie semblent gérables et faciles à résoudre.',
      '[JU.OPP.SU]': 'Les opportunités apparaissent tout autour de vous mais semblent toujours défier votre statu quo. Vous devez décider si le risque vaut le changement qu\'il apportera à votre situation. Le choix évident peut aller à l\'encontre de vos meilleurs intérêts.',
      '[JU.SQR.SU]': 'Vous pourriez vivre une période un peu tendue alors que la direction de votre carrière (et le chemin vers le succès) s\'oppose à vos intérêts plus personnels. Cela pourrait être un véritable test pour vous.',
      '[JU.SXT.SU]': 'Tout pourrait arriver en même temps, et ce ne sont que de bonnes nouvelles. La direction de votre carrière reçoit des encouragements, et les problèmes de la vie devraient trouver des solutions faciles.',
      '[JU.TRI.SU]': 'Tout pourrait arriver en même temps, et ce ne sont que de bonnes nouvelles. La direction de votre carrière reçoit des encouragements, et les problèmes de la vie devraient trouver des solutions faciles.',

      // Ajout d'interprétations Jupiter avec Lune
      '[JU.CNJ.MO]': 'Vous bénéficierez d\'une orientation plus intérieure, vous détournant un peu du monde extérieur et permettant à de nouvelles idées et informations de s\'infiltrer. Un tournant professionnel, après lequel vous devriez lâcher un peu prise.',
      '[JU.OPP.MO]': 'Vous pourriez vous retrouver à devoir choisir entre ce qui vous semble juste et approprié et vos sentiments ou émotions personnels. Vos valeurs peuvent aller à l\'encontre de vos besoins émotionnels.',
      '[JU.SQR.MO]': 'Il peut y avoir un conflit entre vos valeurs et vos sentiments ou émotions. Ce qui vous semble juste peut ne pas vous faire du bien émotionnellement.',
      '[JU.SXT.MO]': 'Vos sentiments et émotions sont en harmonie avec vos valeurs et votre sens de ce qui est juste. Vous pouvez faire confiance à vos instincts.',
      '[JU.TRI.MO]': 'Vos sentiments et émotions sont en harmonie avec vos valeurs et votre sens de ce qui est juste. Vous pouvez faire confiance à vos instincts.',

      // Ajout d'interprétations Jupiter avec Mercure
      '[JU.CNJ.ME]': 'Votre mental et votre sens des valeurs travaillent ensemble. Excellente période pour l\'apprentissage, l\'enseignement et la communication de vos idéaux.',
      '[JU.OPP.ME]': 'Conflit possible entre vos idées et vos valeurs. Vous pourriez avoir des difficultés à communiquer vos convictions.',
      '[JU.SQR.ME]': 'Tension entre votre mental et vos croyances. Attention aux jugements hâtifs ou aux opinions trop tranchées.',
      '[JU.SXT.ME]': 'Vos idées et vos valeurs se soutiennent mutuellement. Excellente période pour l\'étude et la communication.',
      '[JU.TRI.ME]': 'Vos idées et vos valeurs se soutiennent mutuellement. Excellente période pour l\'étude et la communication.',

      // Ajout d'interprétations Jupiter avec Vénus
      '[JU.CNJ.VE]': 'Union harmonieuse entre vos valeurs morales et esthétiques. Période favorable pour l\'art, l\'amour et les plaisirs raffinés.',
      '[JU.OPP.VE]': 'Possible conflit entre vos idéaux et vos désirs de plaisir. Attention aux excès ou aux dépenses excessives.',
      '[JU.SQR.VE]': 'Tension entre vos valeurs morales et vos goûts esthétiques. Risque d\'indulgence excessive.',
      '[JU.SXT.VE]': 'Harmonie entre vos idéaux et vos goûts. Période favorable pour les arts et les relations harmonieuses.',
      '[JU.TRI.VE]': 'Harmonie entre vos idéaux et vos goûts. Période favorable pour les arts et les relations harmonieuses.',

      // Ajout d'interprétations Jupiter avec Jupiter
      '[JU.CNJ.JU]': 'Retour de Jupiter sur lui-même (cycle de 12 ans). Nouveau cycle d\'expansion et de croissance personnelle commence.',
      '[JU.OPP.JU]': 'Opposition de Jupiter (6 ans après la conjonction). Moment de réévaluation de vos croyances et objectifs.',
      '[JU.SQR.JU]': 'Carré de Jupiter (3 ou 9 ans). Défis dans votre croissance personnelle, remise en question nécessaire.',
      '[JU.SXT.JU]': 'Sextile de Jupiter. Opportunités de croissance et d\'expansion dans un domaine de votre vie.',
      '[JU.TRI.JU]': 'Trigone de Jupiter (4 ou 8 ans). Période très favorable pour l\'expansion et la réalisation de vos objectifs.',

      // Ajout d'interprétations Jupiter avec Saturne
      '[JU.CNJ.SA]': 'Union de l\'expansion et de la restriction. Croissance disciplinée et structurée. Sagesse pratique.',
      '[JU.OPP.SA]': 'Conflit entre expansion et limitation. Vous devez trouver un équilibre entre optimisme et réalisme.',
      '[JU.SQR.SA]': 'Tension entre croissance et discipline. Les obstacles peuvent freiner vos projets d\'expansion.',
      '[JU.SXT.SA]': 'Croissance soutenue par la discipline. Vos projets d\'expansion sont bien structurés.',
      '[JU.TRI.SA]': 'Croissance soutenue par la discipline. Vos projets d\'expansion sont bien structurés.',

      // Ajout d'interprétations Jupiter avec Uranus
      '[JU.CNJ.UR]': 'Expansion soudaine et révolutionnaire. Changements majeurs dans votre vision du monde et vos croyances.',
      '[JU.OPP.UR]': 'Conflit entre tradition et innovation. Vos croyances sont remises en question par des idées révolutionnaires.',
      '[JU.SQR.UR]': 'Tension entre expansion et révolution. Changements brusques qui perturbent vos plans.',
      '[JU.SXT.UR]': 'Innovation soutenue par la sagesse. Vos idées progressistes trouvent un terrain favorable.',
      '[JU.TRI.UR]': 'Innovation soutenue par la sagesse. Vos idées progressistes trouvent un terrain favorable.',

      // Ajout d'interprétations Jupiter avec Neptune
      '[JU.CNJ.NE]': 'Union de la sagesse et de l\'inspiration. Période très spirituelle et créative. Attention aux illusions.',
      '[JU.OPP.NE]': 'Conflit entre idéaux et réalité. Risque de désillusion ou de confusion dans vos croyances.',
      '[JU.SQR.NE]': 'Tension entre expansion et illusion. Attention aux projets irréalistes ou aux faux espoirs.',
      '[JU.SXT.NE]': 'Inspiration soutenue par la sagesse. Excellente période pour la créativité et la spiritualité.',
      '[JU.TRI.NE]': 'Inspiration soutenue par la sagesse. Excellente période pour la créativité et la spiritualité.',

      // Ajout d'interprétations Jupiter avec Pluton
      '[JU.CNJ.PL]': 'Transformation profonde de vos croyances et valeurs. Pouvoir de régénération et de renaissance spirituelle.',
      '[JU.OPP.PL]': 'Conflit entre expansion et transformation. Vos croyances sont remises en question en profondeur.',
      '[JU.SQR.PL]': 'Tension entre croissance et transformation. Changements forcés dans votre vision du monde.',
      '[JU.SXT.PL]': 'Transformation soutenue par la sagesse. Évolution positive de vos croyances et valeurs.',
      '[JU.TRI.PL]': 'Transformation soutenue par la sagesse. Évolution positive de vos croyances et valeurs.',

      // Ajout d'interprétations Saturne avec Lune
      '[SA.CNJ.MO]': 'Vous pourriez vous sentir un peu déprimé ou découragé en ce moment. Vous devez peut-être faire face à certaines réalités concernant votre situation de vie et vos responsabilités. Un moment pour être pratique et réaliste.',
      '[SA.OPP.MO]': 'Cela pourrait être un moment où tout autour de vous (votre situation de vie) semble être en fluctuation et même en tumulte. Vous pourriez vous sentir opposé et défié par les autres. Travailler sur des problèmes mis en scène avec d\'autres sera à l\'ordre du jour et difficile à éviter.',
      '[SA.SQR.MO]': 'Vous pourriez vous sentir bloqué émotionnellement, incapable d\'exprimer vos sentiments. Il peut y avoir des conflits entre vos responsabilités et vos besoins émotionnels.',
      '[SA.SXT.MO]': 'Un bon moment pour organiser votre vie émotionnelle et domestique. Vous pouvez faire des progrès pratiques dans votre situation de vie.',
      '[SA.TRI.MO]': 'Un bon moment pour organiser votre vie émotionnelle et domestique. Vous pouvez faire des progrès pratiques dans votre situation de vie.',

      // Ajout d'interprétations Saturne avec Soleil
      '[SA.CNJ.SU]': 'Un moment pour se mettre au travail et consolider votre carrière. Vos capacités d\'organisation et votre sens des responsabilités seront ce qui vous guidera et s\'avérera fructueux.',
      '[SA.OPP.SU]': 'Les opportunités peuvent aller à l\'encontre de votre propre sentiment de sécurité et de responsabilité. Les choix de carrière peuvent exiger que vous preniez un risque.',
      '[SA.SQR.SU]': 'Votre chemin ou votre carrière peut être en porte-à-faux avec votre propre sentiment de sécurité et vos responsabilités. Un compromis difficile à trouver.',
      '[SA.SXT.SU]': 'Un vrai moment pour se mettre au travail et consolider votre carrière. Vos capacités d\'organisation seront ce qui vous guidera.',
      '[SA.TRI.SU]': 'Un vrai moment pour se mettre au travail et consolider votre carrière. Vos capacités d\'organisation seront ce qui vous guidera.',

      // Ajout d'interprétations Saturne avec Mars
      '[SA.CNJ.MA]': 'Vous pourriez vous sentir frustré et bloqué dans vos actions. Il est temps de faire preuve de patience et de persévérance. Les efforts soutenus porteront leurs fruits.',
      '[SA.OPP.MA]': 'Vos actions et ambitions peuvent être contrariées par des obstacles ou des responsabilités. Il faut trouver un équilibre entre action et prudence.',
      '[SA.SQR.MA]': 'Tension entre votre désir d\'agir et les limitations qui vous sont imposées. La frustration peut être forte, mais la discipline est nécessaire.',
      '[SA.SXT.MA]': 'Vos actions sont soutenues par la discipline et la méthode. C\'est un bon moment pour des efforts soutenus et organisés.',
      '[SA.TRI.MA]': 'Vos actions sont soutenues par la discipline et la méthode. C\'est un bon moment pour des efforts soutenus et organisés.',

      // Ajout d'interprétations Saturne avec Mercure
      '[SA.CNJ.ME]': 'Votre mental devient plus discipliné et méthodique. Excellente période pour les études sérieuses et la concentration.',
      '[SA.OPP.ME]': 'Conflit entre votre besoin de structure et votre flexibilité mentale. Attention à la rigidité de pensée.',
      '[SA.SQR.ME]': 'Tension mentale et difficultés de communication. Votre pensée peut être bloquée ou pessimiste.',
      '[SA.SXT.ME]': 'Votre mental est soutenu par la discipline. Excellente période pour l\'apprentissage méthodique.',
      '[SA.TRI.ME]': 'Votre mental est soutenu par la discipline. Excellente période pour l\'apprentissage méthodique.',

      // Ajout d'interprétations Saturne avec Vénus
      '[SA.CNJ.VE]': 'Vos relations et vos plaisirs deviennent plus sérieux et engagés. Période de stabilisation affective.',
      '[SA.OPP.VE]': 'Conflit entre vos responsabilités et vos désirs de plaisir. Les relations peuvent être mises à l\'épreuve.',
      '[SA.SQR.VE]': 'Tension dans vos relations et vos valeurs esthétiques. Période de restrictions affectives.',
      '[SA.SXT.VE]': 'Vos relations sont stabilisées par la maturité. Amour durable et engagements sérieux.',
      '[SA.TRI.VE]': 'Vos relations sont stabilisées par la maturité. Amour durable et engagements sérieux.',

      // Ajout d'interprétations Saturne avec Jupiter
      '[SA.CNJ.JU]': 'Union de la discipline et de l\'expansion. Croissance lente mais solide. Sagesse pratique.',
      '[SA.OPP.JU]': 'Conflit entre limitation et expansion. Vous devez équilibrer prudence et optimisme.',
      '[SA.SQR.JU]': 'Tension entre discipline et croissance. Les restrictions freinent vos projets d\'expansion.',
      '[SA.SXT.JU]': 'Discipline soutenue par la sagesse. Vos projets de croissance sont bien structurés.',
      '[SA.TRI.JU]': 'Discipline soutenue par la sagesse. Vos projets de croissance sont bien structurés.',

      // Ajout d'interprétations Saturne avec Saturne
      '[SA.CNJ.SA]': 'Retour de Saturne (cycle de 29 ans). Nouveau cycle de maturité et de responsabilités commence.',
      '[SA.OPP.SA]': 'Opposition de Saturne (14-15 ans). Remise en question de vos structures et responsabilités.',
      '[SA.SQR.SA]': 'Carré de Saturne (7 ou 21 ans). Défis majeurs dans votre développement personnel et professionnel.',
      '[SA.SXT.SA]': 'Sextile de Saturne. Opportunités de consolidation et de structuration.',
      '[SA.TRI.SA]': 'Trigone de Saturne. Période favorable pour la construction et la stabilisation.',

      // Ajout d'interprétations Saturne avec Uranus
      '[SA.CNJ.UR]': 'Union de la tradition et de l\'innovation. Changements structurés et révolution disciplinée.',
      '[SA.OPP.UR]': 'Conflit entre tradition et révolution. Tension entre stabilité et changement.',
      '[SA.SQR.UR]': 'Tension entre structure et liberté. Changements forcés qui perturbent vos fondations.',
      '[SA.SXT.UR]': 'Innovation soutenue par la structure. Changements progressifs et bien planifiés.',
      '[SA.TRI.UR]': 'Innovation soutenue par la structure. Changements progressifs et bien planifiés.',

      // Ajout d'interprétations Saturne avec Neptune
      '[SA.CNJ.NE]': 'Union de la réalité et du rêve. Concrétisation de vos idéaux et inspirations.',
      '[SA.OPP.NE]': 'Conflit entre réalisme et idéalisme. Désillusion possible face à la réalité.',
      '[SA.SQR.NE]': 'Tension entre structure et illusion. Confusion dans vos responsabilités.',
      '[SA.SXT.NE]': 'Inspiration soutenue par la discipline. Réalisation concrète de vos rêves.',
      '[SA.TRI.NE]': 'Inspiration soutenue par la discipline. Réalisation concrète de vos rêves.',

      // Ajout d'interprétations Saturne avec Pluton
      '[SA.CNJ.PL]': 'Transformation profonde de vos structures. Reconstruction complète de vos fondations.',
      '[SA.OPP.PL]': 'Conflit entre structure et transformation. Destruction nécessaire pour reconstruire.',
      '[SA.SQR.PL]': 'Tension entre stabilité et transformation. Changements forcés dans vos structures.',
      '[SA.SXT.PL]': 'Transformation soutenue par la discipline. Évolution structurée et méthodique.',
      '[SA.TRI.PL]': 'Transformation soutenue par la discipline. Évolution structurée et méthodique.',

      // Ajout d'interprétations Mars avec Lune
      '[MA.CNJ.MO]': 'Vos émotions sont intenses et votre énergie émotionnelle est à son maximum. Vous pourriez être plus impulsif que d\'habitude dans vos réactions.',
      '[MA.OPP.MO]': 'Conflit possible entre vos désirs d\'action et vos besoins émotionnels. Vous pourriez vous sentir tiraillé entre différentes directions.',
      '[MA.SQR.MO]': 'Tension émotionnelle qui peut se manifester par de l\'irritabilité ou de l\'impatience. Attention aux réactions impulsives.',
      '[MA.SXT.MO]': 'Vos émotions soutiennent vos actions. C\'est un bon moment pour agir selon vos sentiments et intuitions.',
      '[MA.TRI.MO]': 'Vos émotions soutiennent vos actions. C\'est un bon moment pour agir selon vos sentiments et intuitions.',

      // Ajout d'interprétations Mars avec Soleil
      '[MA.CNJ.SU]': 'Votre énergie et votre dynamisme sont à leur maximum. C\'est un excellent moment pour prendre des initiatives et passer à l\'action.',
      '[MA.OPP.SU]': 'Possible conflit entre votre ego et vos actions. Vous pourriez avoir tendance à être trop agressif ou impulsif.',
      '[MA.SQR.SU]': 'Tension entre votre volonté et vos actions. Attention à ne pas être trop impétueux ou à forcer les situations.',
      '[MA.SXT.SU]': 'Votre volonté et vos actions sont en harmonie. C\'est un moment favorable pour réaliser vos projets.',
      '[MA.TRI.SU]': 'Votre volonté et vos actions sont en harmonie. C\'est un moment favorable pour réaliser vos projets.',

      // Ajout d'interprétations Vénus avec Lune
      '[VE.CNJ.MO]': 'Vos émotions et vos sentiments sont en harmonie avec vos valeurs esthétiques et relationnelles. Période favorable pour l\'amour et les plaisirs.',
      '[VE.OPP.MO]': 'Possible tension entre vos besoins émotionnels et vos désirs de plaisir ou vos relations. Équilibre à trouver.',
      '[VE.SQR.MO]': 'Conflit entre vos émotions et vos valeurs esthétiques ou relationnelles. Attention aux excès ou aux dépenses impulsives.',
      '[VE.SXT.MO]': 'Harmonie entre vos émotions et vos goûts. Bon moment pour les activités créatives et les relations.',
      '[VE.TRI.MO]': 'Harmonie entre vos émotions et vos goûts. Bon moment pour les activités créatives et les relations.',

      // Ajout d'interprétations Vénus avec Soleil
      '[VE.CNJ.SU]': 'Votre charme et votre magnétisme sont à leur maximum. Période favorable pour l\'amour, l\'art et les plaisirs de la vie.',
      '[VE.OPP.SU]': 'Possible conflit entre votre ego et vos relations ou vos valeurs esthétiques. Attention à l\'orgueil en amour.',
      '[VE.SQR.SU]': 'Tension entre votre volonté et vos désirs de plaisir. Attention aux excès ou aux conflits relationnels.',
      '[VE.SXT.SU]': 'Votre personnalité rayonne de charme et d\'harmonie. Excellent moment pour les relations et la créativité.',
      '[VE.TRI.SU]': 'Votre personnalité rayonne de charme et d\'harmonie. Excellent moment pour les relations et la créativité.',

      // Ajout d'interprétations Vénus avec Mars
      '[VE.CNJ.MA]': 'Union de l\'amour et de la passion. Vos désirs et vos actions sont en harmonie avec vos sentiments.',
      '[VE.OPP.MA]': 'Tension entre vos désirs passionnels et vos valeurs relationnelles. Possible conflit entre amour et désir.',
      '[VE.SQR.MA]': 'Conflit entre vos actions et vos valeurs esthétiques ou relationnelles. Attention aux impulsions destructrices.',
      '[VE.SXT.MA]': 'Vos actions sont guidées par l\'amour et l\'harmonie. Bon équilibre entre passion et tendresse.',
      '[VE.TRI.MA]': 'Vos actions sont guidées par l\'amour et l\'harmonie. Bon équilibre entre passion et tendresse.',

      // Ajout d'interprétations Mercure avec Lune
      '[ME.CNJ.MO]': 'Vos pensées et vos émotions sont étroitement liées. Excellente période pour exprimer vos sentiments et communiquer.',
      '[ME.OPP.MO]': 'Possible conflit entre votre logique et vos émotions. Difficulté à exprimer clairement vos sentiments.',
      '[ME.SQR.MO]': 'Tension entre vos pensées et vos émotions. Attention aux malentendus ou aux réactions émotionnelles excessives.',
      '[ME.SXT.MO]': 'Harmonie entre votre mental et vos émotions. Excellente communication et compréhension intuitive.',
      '[ME.TRI.MO]': 'Harmonie entre votre mental et vos émotions. Excellente communication et compréhension intuitive.',

      // Ajout d'interprétations Mercure avec Soleil
      '[ME.CNJ.SU]': 'Votre mental et votre volonté sont parfaitement alignés. Excellente période pour la communication et les décisions.',
      '[ME.OPP.SU]': 'Possible conflit entre votre ego et vos idées. Attention à l\'orgueil intellectuel ou aux malentendus.',
      '[ME.SQR.SU]': 'Tension entre votre volonté et vos pensées. Difficulté à exprimer clairement vos intentions.',
      '[ME.SXT.SU]': 'Votre intelligence et votre volonté travaillent en harmonie. Excellente période pour les projets intellectuels.',
      '[ME.TRI.SU]': 'Votre intelligence et votre volonté travaillent en harmonie. Excellente période pour les projets intellectuels.',

      // Ajout d'interprétations Mercure avec Mars
      '[ME.CNJ.MA]': 'Vos pensées et vos actions sont parfaitement synchronisées. Excellente période pour passer de l\'idée à l\'action.',
      '[ME.OPP.MA]': 'Conflit possible entre vos idées et vos actions. Attention aux paroles blessantes ou aux décisions hâtives.',
      '[ME.SQR.MA]': 'Tension entre votre mental et vos impulsions. Attention à l\'agressivité verbale ou aux erreurs de jugement.',
      '[ME.SXT.MA]': 'Vos idées soutiennent efficacement vos actions. Bon moment pour les projets nécessitant réflexion et action.',
      '[ME.TRI.MA]': 'Vos idées soutiennent efficacement vos actions. Bon moment pour les projets nécessitant réflexion et action.',

      // Ajout d'interprétations Mars avec Mercure
      '[MA.CNJ.ME]': 'Vos actions et vos pensées sont parfaitement alignées. Excellente période pour la communication dynamique.',
      '[MA.OPP.ME]': 'Conflit entre vos impulsions et votre logique. Attention aux décisions précipitées.',
      '[MA.SQR.ME]': 'Tension entre action et réflexion. Risque d\'agressivité verbale ou de malentendus.',
      '[MA.SXT.ME]': 'Vos actions sont guidées par l\'intelligence. Communication efficace et décisions rapides.',
      '[MA.TRI.ME]': 'Vos actions sont guidées par l\'intelligence. Communication efficace et décisions rapides.',

      // Ajout d'interprétations Mars avec Vénus
      '[MA.CNJ.VE]': 'Union de la passion et de l\'amour. Vos désirs et vos sentiments sont en parfaite harmonie.',
      '[MA.OPP.VE]': 'Conflit entre passion et tendresse. Tension dans vos relations amoureuses.',
      '[MA.SQR.VE]': 'Tension entre désir et amour. Risque de conflits dans vos relations.',
      '[MA.SXT.VE]': 'Passion équilibrée par l\'amour. Harmonie dans vos relations et vos désirs.',
      '[MA.TRI.VE]': 'Passion équilibrée par l\'amour. Harmonie dans vos relations et vos désirs.',

      // Ajout d'interprétations Mars avec Jupiter
      '[MA.CNJ.JU]': 'Vos actions sont soutenues par l\'optimisme et la confiance. Excellente période pour entreprendre.',
      '[MA.OPP.JU]': 'Conflit entre action et sagesse. Risque d\'excès ou d\'imprudence dans vos entreprises.',
      '[MA.SQR.JU]': 'Tension entre impulsion et jugement. Attention aux actions trop ambitieuses.',
      '[MA.SXT.JU]': 'Vos actions sont guidées par la sagesse. Entreprises favorisées par la chance.',
      '[MA.TRI.JU]': 'Vos actions sont guidées par la sagesse. Entreprises favorisées par la chance.',

      // Ajout d'interprétations Mars avec Saturne
      '[MA.CNJ.SA]': 'Vos actions deviennent plus disciplinées et méthodiques. Efforts soutenus et persévérance.',
      '[MA.OPP.SA]': 'Conflit entre impulsion et discipline. Vos actions sont freinées par des obstacles.',
      '[MA.SQR.SA]': 'Tension entre désir d\'agir et limitations. Frustration et blocages possibles.',
      '[MA.SXT.SA]': 'Vos actions sont soutenues par la discipline. Efforts méthodiques et durables.',
      '[MA.TRI.SA]': 'Vos actions sont soutenues par la discipline. Efforts méthodiques et durables.',

      // Ajout d'interprétations Mars avec Uranus
      '[MA.CNJ.UR]': 'Actions soudaines et révolutionnaires. Énergie explosive et changements brusques.',
      '[MA.OPP.UR]': 'Conflit entre action et révolution. Tension entre routine et changement.',
      '[MA.SQR.UR]': 'Tension entre impulsion et innovation. Actions imprévisibles et déstabilisantes.',
      '[MA.SXT.UR]': 'Actions innovantes et originales. Énergie créatrice et révolutionnaire.',
      '[MA.TRI.UR]': 'Actions innovantes et originales. Énergie créatrice et révolutionnaire.',

      // Ajout d'interprétations Mars avec Neptune
      '[MA.CNJ.NE]': 'Actions inspirées et créatives. Énergie spirituelle et artistique.',
      '[MA.OPP.NE]': 'Conflit entre action et inspiration. Confusion dans vos motivations.',
      '[MA.SQR.NE]': 'Tension entre impulsion et illusion. Risque d\'actions mal dirigées.',
      '[MA.SXT.NE]': 'Actions inspirées par l\'intuition. Créativité et spiritualité actives.',
      '[MA.TRI.NE]': 'Actions inspirées par l\'intuition. Créativité et spiritualité actives.',

      // Ajout d'interprétations Mars avec Pluton
      '[MA.CNJ.PL]': 'Actions transformatrices et puissantes. Énergie de régénération et de renaissance.',
      '[MA.OPP.PL]': 'Conflit entre action et transformation. Luttes de pouvoir possibles.',
      '[MA.SQR.PL]': 'Tension entre impulsion et transformation. Actions destructrices ou régénératrices.',
      '[MA.SXT.PL]': 'Actions transformatrices positives. Pouvoir de régénération et de renouveau.',
      '[MA.TRI.PL]': 'Actions transformatrices positives. Pouvoir de régénération et de renouveau.',

      // Ajout d'interprétations Mars avec Mars
      '[MA.CNJ.MA]': 'Retour de Mars (cycle de 2 ans). Nouveau cycle d\'action et d\'initiative commence.',
      '[MA.OPP.MA]': 'Opposition de Mars. Conflit entre différentes directions d\'action.',
      '[MA.SQR.MA]': 'Carré de Mars. Tension et frustration dans vos actions.',
      '[MA.SXT.MA]': 'Sextile de Mars. Opportunités d\'action et d\'initiative.',
      '[MA.TRI.MA]': 'Trigone de Mars. Période très favorable pour l\'action et les initiatives.',

      // Ajout d'interprétations Vénus avec Jupiter
      '[VE.CNJ.JU]': 'Union de l\'amour et de la sagesse. Période très favorable pour les relations et l\'art.',
      '[VE.OPP.JU]': 'Conflit entre plaisir et sagesse. Risque d\'excès dans les plaisirs ou les dépenses.',
      '[VE.SQR.JU]': 'Tension entre goûts et valeurs morales. Attention aux excès ou à l\'indulgence.',
      '[VE.SXT.JU]': 'Harmonie entre amour et sagesse. Relations favorisées par la générosité.',
      '[VE.TRI.JU]': 'Harmonie entre amour et sagesse. Relations favorisées par la générosité.',

      // Ajout d'interprétations Vénus avec Saturne
      '[VE.CNJ.SA]': 'Amour mature et engagé. Relations sérieuses et durables.',
      '[VE.OPP.SA]': 'Conflit entre plaisir et responsabilité. Relations mises à l\'épreuve.',
      '[VE.SQR.SA]': 'Tension dans les relations. Restrictions affectives ou financières.',
      '[VE.SXT.SA]': 'Relations stabilisées par la maturité. Amour durable et fidèle.',
      '[VE.TRI.SA]': 'Relations stabilisées par la maturité. Amour durable et fidèle.',

      // Ajout d'interprétations Vénus avec Uranus
      '[VE.CNJ.UR]': 'Amour soudain et révolutionnaire. Changements brusques dans vos relations.',
      '[VE.OPP.UR]': 'Conflit entre tradition et innovation en amour. Relations instables.',
      '[VE.SQR.UR]': 'Tension entre stabilité et liberté amoureuse. Ruptures possibles.',
      '[VE.SXT.UR]': 'Relations originales et libres. Amour innovant et créatif.',
      '[VE.TRI.UR]': 'Relations originales et libres. Amour innovant et créatif.',

      // Ajout d'interprétations Vénus avec Neptune
      '[VE.CNJ.NE]': 'Amour idéalisé et spirituel. Romance et créativité artistique.',
      '[VE.OPP.NE]': 'Conflit entre réalité et idéal amoureux. Risque de désillusion.',
      '[VE.SQR.NE]': 'Tension entre amour et illusion. Confusion dans les relations.',
      '[VE.SXT.NE]': 'Amour inspiré et créatif. Relations spirituelles et artistiques.',
      '[VE.TRI.NE]': 'Amour inspiré et créatif. Relations spirituelles et artistiques.',

      // Ajout d'interprétations Vénus avec Pluton
      '[VE.CNJ.PL]': 'Amour transformateur et passionnel. Relations intenses et profondes.',
      '[VE.OPP.PL]': 'Conflit entre amour et pouvoir. Luttes dans les relations.',
      '[VE.SQR.PL]': 'Tension entre amour et transformation. Relations destructrices ou régénératrices.',
      '[VE.SXT.PL]': 'Amour transformateur positif. Relations profondes et évolutives.',
      '[VE.TRI.PL]': 'Amour transformateur positif. Relations profondes et évolutives.',

      // Ajout d'interprétations Vénus avec Vénus
      '[VE.CNJ.VE]': 'Retour de Vénus (cycle de 8 mois). Nouveau cycle d\'amour et de créativité.',
      '[VE.OPP.VE]': 'Opposition de Vénus. Réévaluation de vos relations et valeurs.',
      '[VE.SQR.VE]': 'Carré de Vénus. Défis dans vos relations et votre créativité.',
      '[VE.SXT.VE]': 'Sextile de Vénus. Opportunités amoureuses et créatives.',
      '[VE.TRI.VE]': 'Trigone de Vénus. Période très favorable pour l\'amour et l\'art.',

      // Ajout d'interprétations Mercure avec Jupiter
      '[ME.CNJ.JU]': 'Mental élargi et optimiste. Excellente période pour l\'apprentissage et l\'enseignement.',
      '[ME.OPP.JU]': 'Conflit entre détails et vision globale. Attention aux jugements hâtifs.',
      '[ME.SQR.JU]': 'Tension entre analyse et synthèse. Risque de dispersion mentale.',
      '[ME.SXT.JU]': 'Intelligence soutenue par la sagesse. Communication inspirée et élargie.',
      '[ME.TRI.JU]': 'Intelligence soutenue par la sagesse. Communication inspirée et élargie.',

      // Ajout d'interprétations Mercure avec Saturne
      '[ME.CNJ.SA]': 'Mental discipliné et méthodique. Excellente concentration et organisation.',
      '[ME.OPP.SA]': 'Conflit entre flexibilité et rigidité mentale. Communication bloquée.',
      '[ME.SQR.SA]': 'Tension mentale et pessimisme. Difficultés de communication.',
      '[ME.SXT.SA]': 'Mental soutenu par la discipline. Apprentissage méthodique et durable.',
      '[ME.TRI.SA]': 'Mental soutenu par la discipline. Apprentissage méthodique et durable.',

      // Ajout d'interprétations Mercure avec Uranus
      '[ME.CNJ.UR]': 'Mental révolutionnaire et innovant. Idées originales et géniales.',
      '[ME.OPP.UR]': 'Conflit entre logique et intuition. Pensée erratique.',
      '[ME.SQR.UR]': 'Tension mentale et nervosité. Idées déstabilisantes.',
      '[ME.SXT.UR]': 'Mental innovant et créatif. Communication originale et inspirée.',
      '[ME.TRI.UR]': 'Mental innovant et créatif. Communication originale et inspirée.',

      // Ajout d'interprétations Mercure avec Neptune
      '[ME.CNJ.NE]': 'Mental inspiré et intuitif. Communication spirituelle et artistique.',
      '[ME.OPP.NE]': 'Conflit entre logique et intuition. Confusion mentale possible.',
      '[ME.SQR.NE]': 'Tension entre raison et imagination. Risque d\'illusions.',
      '[ME.SXT.NE]': 'Mental inspiré par l\'intuition. Communication créative et spirituelle.',
      '[ME.TRI.NE]': 'Mental inspiré par l\'intuition. Communication créative et spirituelle.',

      // Ajout d'interprétations Mercure avec Pluton
      '[ME.CNJ.PL]': 'Mental transformateur et pénétrant. Pensée profonde et investigatrice.',
      '[ME.OPP.PL]': 'Conflit entre surface et profondeur. Communication intense.',
      '[ME.SQR.PL]': 'Tension mentale et obsessions. Pensées destructrices ou régénératrices.',
      '[ME.SXT.PL]': 'Mental transformateur positif. Communication profonde et révélatrice.',
      '[ME.TRI.PL]': 'Mental transformateur positif. Communication profonde et révélatrice.',

      // Ajout d'interprétations Mercure avec Mercure
      '[ME.CNJ.ME]': 'Retour de Mercure (cycle de 4 mois). Nouveau cycle de communication et d\'apprentissage.',
      '[ME.OPP.ME]': 'Opposition de Mercure. Réévaluation de vos idées et communications.',
      '[ME.SQR.ME]': 'Carré de Mercure. Défis dans la communication et l\'apprentissage.',
      '[ME.SXT.ME]': 'Sextile de Mercure. Opportunités de communication et d\'apprentissage.',
      '[ME.TRI.ME]': 'Trigone de Mercure. Période très favorable pour la communication et les études.'
    };

    return interpretationDatabase[key] || null;
  }



  // Initialize interpretations button functionality
  function initInterpretationsButton() {
    const interpretationsBtn = document.getElementById('interpretations-btn');
    const interpretationsModal = document.getElementById('interpretations-modal');
    const closeInterpretations = document.getElementById('close-interpretations');
    const interpretationsContent = document.getElementById('interpretations-content');

    if (!interpretationsBtn || !interpretationsModal) return;

    // Open modal when button is clicked
    interpretationsBtn.addEventListener('click', function() {
      loadInterpretations();
      interpretationsModal.style.display = 'block';
    });

    // Close modal when X is clicked
    if (closeInterpretations) {
      closeInterpretations.addEventListener('click', function() {
        interpretationsModal.style.display = 'none';
      });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === interpretationsModal) {
        interpretationsModal.style.display = 'none';
      }
    });

    // Load interpretations content
    function loadInterpretations() {
      if (!interpretationsContent) return;

      // Get current aspects from the chart
      const currentAspects = getCurrentAspects();

      if (!currentAspects || currentAspects.length === 0) {
        interpretationsContent.innerHTML = `
          <div class="interpretations-intro">
            <h4>Interprétations des Aspects Transit-Natal</h4>
            <p>Aucun aspect trouvé pour la date actuelle. Calculez d'abord un thème pour voir les interprétations spécifiques basées sur le fichier "Francais - Transit natal.txt".</p>
          </div>
        `;
        return;
      }

      let content = '<div class="interpretations-intro">';
      content += '<h4>Interprétations des Aspects Transit-Natal Actuels</h4>';
      content += '<p>Interprétations spécifiques basées sur le fichier "Francais - Transit natal.txt" pour vos aspects actuels.</p>';
      content += '</div>';

      // Group aspects by transit planet for better organization
      const aspectsByPlanet = {};
      currentAspects.forEach(aspect => {
        const transitPlanet = getPlanetFrenchName(aspect.transitPlanet);
        if (!aspectsByPlanet[transitPlanet]) {
          aspectsByPlanet[transitPlanet] = [];
        }
        aspectsByPlanet[transitPlanet].push(aspect);
      });

      // Display interpretations for each planet
      Object.keys(aspectsByPlanet).forEach(planetName => {
        content += `<div class="interpretation-section">`;
        content += `<div class="interpretation-title">Transits de ${planetName}</div>`;

        aspectsByPlanet[planetName].forEach(aspect => {
          const transitPlanet = getPlanetFrenchName(aspect.transitPlanet);
          const natalPlanet = getPlanetFrenchName(aspect.natalPlanet);
          const aspectType = getAspectFrenchName(aspect.aspectType);

          // Get specific interpretation
          const interpretation = getSpecificInterpretation(aspect.transitPlanet, aspect.natalPlanet, aspect.aspectType);

          if (interpretation) {
            content += `<div class="aspect-interpretation">`;
            content += `<strong>${transitPlanet} ${aspectType} ${natalPlanet}:</strong><br>`;
            content += `<span class="interpretation-text">${interpretation}</span>`;
            content += `<div class="aspect-details">Orb: ${aspect.orb}° | Positions: ${aspect.transitPosition}° - ${aspect.natalPosition}°</div>`;
            content += `</div><br>`;
          }
        });

        content += `</div>`;
      });

      if (Object.keys(aspectsByPlanet).length === 0) {
        content += '<div class="no-interpretations">Aucune interprétation spécifique disponible pour les aspects actuels.</div>';
      }

      interpretationsContent.innerHTML = content;
    }

    // Function to get current aspects from the displayed chart
    function getCurrentAspects() {
      // Try to get aspects from the global variables or DOM
      if (window.currentAspects) {
        return window.currentAspects;
      }

      // If no global aspects, try to extract from the aspects display
      const aspectsContainer = document.querySelector('.aspects-container');
      if (!aspectsContainer) return [];

      // This is a fallback - ideally we should store aspects globally when calculating
      return [];
    }
  }

  // Initialize time navigation functionality
  function initTimeNavigation() {
    // Add event listeners to all time navigation buttons (updated selector)
    const timeNavButtons = document.querySelectorAll('.time-nav-btn-mini');
    timeNavButtons.forEach(button => {
      button.addEventListener('click', function() {
        const unit = this.getAttribute('data-unit');
        const value = parseInt(this.getAttribute('data-value'));
        adjustTransitTime(unit, value);
      });
    });

    // Add event listener to reset button
    if (resetToNowBtn) {
      resetToNowBtn.addEventListener('click', function() {
        resetTransitToNow();
      });
    }
  }

  // Function to adjust transit time
  function adjustTransitTime(unit, value) {
    if (isAutoTransitMode) return; // Only work in manual mode

    // Get current transit date from inputs
    const currentDate = new Date(transitDateInput.value);
    currentDate.setHours(
      parseInt(transitHourInput.value) || 0,
      parseInt(transitMinuteInput.value) || 0,
      0
    );

    // Adjust the date based on unit and value
    switch (unit) {
      case 'year':
        currentDate.setFullYear(currentDate.getFullYear() + value);
        break;
      case 'month':
        currentDate.setMonth(currentDate.getMonth() + value);
        break;
      case 'day':
        currentDate.setDate(currentDate.getDate() + value);
        break;
      case 'hour':
        currentDate.setHours(currentDate.getHours() + value);
        break;
      case 'minute':
        currentDate.setMinutes(currentDate.getMinutes() + value);
        break;
    }

    // Update the input fields with the new date/time
    updateTransitInputs(currentDate);

    // Recalculate the chart
    calculateChart();

    // Update day comments display
    if (window.updateDayCommentsDisplay) {
      window.updateDayCommentsDisplay().catch(console.error);
    }
  }

  // Function to update transit inputs with a specific date
  function updateTransitInputs(date) {
    transitDateInput.value = date.toISOString().split('T')[0];
    transitHourInput.value = date.getHours();
    transitMinuteInput.value = date.getMinutes();

    const transitSecondInput = document.getElementById('transit-second');
    if (transitSecondInput) {
      transitSecondInput.value = date.getSeconds();
    }
  }

  // Function to reset transit time to current time
  function resetTransitToNow() {
    if (isAutoTransitMode) return; // Only work in manual mode

    const now = new Date();
    updateTransitInputs(now);
    calculateChart();

    // Update day comments display
    if (window.updateDayCommentsDisplay) {
      window.updateDayCommentsDisplay().catch(console.error);
    }
  }

  // Initialize legend modal functionality
  function initLegendModal() {
    // Show legend modal when button is clicked
    if (showLegendBtn) {
      showLegendBtn.addEventListener('click', function() {
        if (legendModal) {
          legendModal.style.display = 'block';
        }
      });
    }

    // Close legend modal when X is clicked
    if (closeLegendBtn) {
      closeLegendBtn.addEventListener('click', function() {
        if (legendModal) {
          legendModal.style.display = 'none';
        }
      });
    }

    // Close legend modal when clicking outside
    if (legendModal) {
      legendModal.addEventListener('click', function(event) {
        if (event.target === legendModal) {
          legendModal.style.display = 'none';
        }
      });
    }
  }

  // Initialize 360° chart toggle functionality
  function init360ChartModal() {
    // Toggle between 30° and 360° chart when button is clicked
    if (show360ChartBtn) {
      show360ChartBtn.addEventListener('click', function() {
        toggleChartMode();
      });
    }
  }

  // Global variable to track current chart mode
  let isChart360Mode = false;

  // Global variables to store last calculated positions
  window.lastBirthPositions = null;
  window.lastTransitPositions = null;

  // Function to toggle between 30° and 360° chart modes
  function toggleChartMode() {
    const canvas = document.getElementById('circular-zodiac-canvas');
    if (!canvas) return;

    isChart360Mode = !isChart360Mode;

    if (isChart360Mode) {
      // Switch to 360° mode
      show360ChartBtn.textContent = 'Zodiaque 30°';
      show360ChartBtn.style.backgroundColor = '#dc3545'; // Red color

      // Remove 30° mode event listeners
      remove30DegreeEventListeners(canvas);

      draw360ChartOnMain();

      // Setup 360° mode event listeners
      setup360DegreeEventListeners(canvas);
    } else {
      // Switch back to 30° mode
      show360ChartBtn.textContent = 'Carte du Ciel 360°';
      show360ChartBtn.style.backgroundColor = '#28a745'; // Green color

      // Remove 360° mode event listeners
      remove360DegreeEventListeners(canvas);

      drawCircularProportionalZodiac(window.lastBirthPositions, window.lastTransitPositions);

      // Re-setup 30° mode event listeners
      setup30DegreeEventListeners(canvas);
    }
  }

  // Initialize monthly table 360° mode toggle
  function initMonthly360ModeToggle() {
    const monthly360Btn = document.getElementById('monthly-360-mode-btn');
    if (monthly360Btn) {
      monthly360Btn.addEventListener('click', function() {
        toggleMonthlyTableMode();
      });
    }
  }

  // Function to toggle monthly table between 30° and 360° modes
  function toggleMonthlyTableMode() {
    isMonthlyTable360Mode = !isMonthlyTable360Mode;

    const monthly360Btn = document.getElementById('monthly-360-mode-btn');
    if (!monthly360Btn) return;

    if (isMonthlyTable360Mode) {
      // Switch to 360° mode
      monthly360Btn.textContent = '🌍 Mode 30°';
      monthly360Btn.classList.add('active');
    } else {
      // Switch back to 30° mode
      monthly360Btn.textContent = '🌍 Mode 360°';
      monthly360Btn.classList.remove('active');
    }

    // Regenerate all visible tables with new mode
    const activeTab = document.querySelector('.monthly-tab-btn.active');
    if (activeTab) {
      const targetTab = activeTab.getAttribute('data-tab');
      generateMonthlyTable(targetTab);
    }
  }

  // Function to remove 30° mode event listeners
  function remove30DegreeEventListeners(canvas) {
    // Remove aspect line hover handlers
    if (canvas.aspectLineHoverHandler) {
      canvas.removeEventListener('mousemove', canvas.aspectLineHoverHandler);
      canvas.aspectLineHoverHandler = null;
    }
    if (canvas.aspectLineLeaveHandler) {
      canvas.removeEventListener('mouseleave', canvas.aspectLineLeaveHandler);
      canvas.aspectLineLeaveHandler = null;
    }
    if (canvas.aspectLineClickHandler) {
      canvas.removeEventListener('click', canvas.aspectLineClickHandler);
      canvas.aspectLineClickHandler = null;
    }

    // Remove planet click handlers
    if (canvas.planetClickHandler) {
      canvas.removeEventListener('click', canvas.planetClickHandler);
      canvas.planetClickHandler = null;
    }

    // Reset cursor style
    canvas.style.cursor = 'default';

    // Clear stored aspect lines data
    window.aspectLines = [];

    // Clear base image data for hover effects
    if (canvas.baseImageData) {
      canvas.baseImageData = null;
    }
  }

  // Function to setup 30° mode event listeners
  function setup30DegreeEventListeners(canvas) {
    // Re-setup click handlers for transit planets
    if (window.lastTransitPositions) {
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = Math.max(10, Math.min(centerX, centerY) - 30);
      const outerRadius = radius * 0.92;

      setupTransitPlanetClickHandlers(canvas, centerX, centerY, outerRadius, window.lastTransitPositions);
    }

    // Re-setup click handlers for aspect lines
    setupAspectLineClickHandlers(canvas);
  }

  // Function to setup 360° mode event listeners
  function setup360DegreeEventListeners(canvas) {
    // Setup hover handlers for 360° aspect lines
    setup360AspectHoverHandlers(canvas);
  }

  // Function to remove 360° mode event listeners
  function remove360DegreeEventListeners(canvas) {
    // Remove 360° aspect hover handlers
    if (canvas.aspect360HoverHandler) {
      canvas.removeEventListener('mousemove', canvas.aspect360HoverHandler);
      canvas.aspect360HoverHandler = null;
    }
    if (canvas.aspect360LeaveHandler) {
      canvas.removeEventListener('mouseleave', canvas.aspect360LeaveHandler);
      canvas.aspect360LeaveHandler = null;
    }
    if (canvas.aspect360ClickHandler) {
      canvas.removeEventListener('click', canvas.aspect360ClickHandler);
      canvas.aspect360ClickHandler = null;
    }

    // Reset cursor style
    canvas.style.cursor = 'default';

    // Clear tooltip if exists
    const tooltip = document.getElementById('aspect-tooltip-360');
    if (tooltip) {
      tooltip.remove();
    }

    // Clear base image data for 360° hover effects
    if (canvas.baseImageData360) {
      canvas.baseImageData360 = null;
    }
  }

  // Function to setup hover handlers for 360° aspect lines
  function setup360AspectHoverHandlers(canvas) {
    // Store the currently hovered aspect line
    let hoveredAspectLine360 = null;

    // Create hover event listener for 360° aspect lines
    canvas.aspect360HoverHandler = function(event) {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      let foundHoveredLine = null;

      // Check if mouse is near any 360° aspect line
      if (window.aspectLines360) {
        for (let aspectLine of window.aspectLines360) {
          if (isPointNearLine(x, y, aspectLine.x1, aspectLine.y1, aspectLine.x2, aspectLine.y2, 10)) {
            foundHoveredLine = aspectLine;
            break;
          }
        }
      }

      // If hover state changed, redraw with highlight
      if (foundHoveredLine !== hoveredAspectLine360) {
        hoveredAspectLine360 = foundHoveredLine;

        // Change cursor style
        canvas.style.cursor = hoveredAspectLine360 ? 'pointer' : 'default';

        // Redraw 360° chart with highlight
        redraw360ChartWithHighlight(canvas, hoveredAspectLine360);

        // Show or hide tooltip
        if (hoveredAspectLine360) {
          show360AspectTooltip(hoveredAspectLine360, event.clientX, event.clientY);
        } else {
          hide360AspectTooltip();
        }
      }
    };

    // Create mouse leave event listener
    canvas.aspect360LeaveHandler = function(event) {
      if (hoveredAspectLine360) {
        hoveredAspectLine360 = null;
        canvas.style.cursor = 'default';
        redraw360ChartWithHighlight(canvas, null);
        hide360AspectTooltip();
      }
    };

    // Create click event listener for 360° aspect lines
    canvas.aspect360ClickHandler = function(event) {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Check if click is near any 360° aspect line
      if (window.aspectLines360) {
        for (let aspectLine of window.aspectLines360) {
          if (isPointNearLine(x, y, aspectLine.x1, aspectLine.y1, aspectLine.x2, aspectLine.y2, 10)) {
            // Show interpretation popup for this aspect
            show360AspectInterpretation(aspectLine);
            event.stopPropagation(); // Prevent other click handlers
            return;
          }
        }
      }
    };

    // Add the event listeners
    canvas.addEventListener('mousemove', canvas.aspect360HoverHandler);
    canvas.addEventListener('mouseleave', canvas.aspect360LeaveHandler);
    canvas.addEventListener('click', canvas.aspect360ClickHandler);
  }

  // Function to show 360° aspect tooltip
  function show360AspectTooltip(aspectLine, mouseX, mouseY) {
    // Remove existing tooltip
    hide360AspectTooltip();

    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.id = 'aspect-tooltip-360';
    tooltip.style.position = 'fixed';
    tooltip.style.left = (mouseX + 10) + 'px';
    tooltip.style.top = (mouseY - 10) + 'px';
    tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    tooltip.style.color = 'white';
    tooltip.style.padding = '8px 12px';
    tooltip.style.borderRadius = '4px';
    tooltip.style.fontSize = '12px';
    tooltip.style.zIndex = '10000';
    tooltip.style.pointerEvents = 'none';
    tooltip.style.whiteSpace = 'nowrap';
    tooltip.style.border = `2px solid ${aspectLine.color}`;

    // Get planet names in French
    const transitPlanetName = getPlanetFrenchName(aspectLine.transitPlanet);
    const natalPlanetName = getPlanetFrenchName(aspectLine.natalPlanet);

    // Create tooltip content
    tooltip.innerHTML = `
      <div style="font-weight: bold; color: ${aspectLine.color};">${aspectLine.aspectFrenchName}</div>
      <div>${transitPlanetName} (${aspectLine.transitPosition}°) → ${natalPlanetName} (${aspectLine.natalPosition}°)</div>
      <div>Orbe: ${aspectLine.orb}°</div>
    `;

    document.body.appendChild(tooltip);
  }

  // Function to hide 360° aspect tooltip
  function hide360AspectTooltip() {
    const tooltip = document.getElementById('aspect-tooltip-360');
    if (tooltip) {
      tooltip.remove();
    }
  }

  // Function to redraw 360° chart with highlight
  function redraw360ChartWithHighlight(canvas, hoveredLine) {
    if (!window.aspectLines360 || window.aspectLines360.length === 0) return;

    const ctx = canvas.getContext('2d');

    // Store the base canvas if not already stored
    if (!canvas.baseImageData360 && !hoveredLine) {
      canvas.baseImageData360 = ctx.getImageData(0, 0, canvas.width, canvas.height);
      return;
    }

    // If we have a base image and we're clearing highlight, restore it
    if (canvas.baseImageData360 && !hoveredLine) {
      ctx.putImageData(canvas.baseImageData360, 0, 0);
      return;
    }

    // If we don't have base image data, redraw the entire chart
    if (!canvas.baseImageData360) {
      draw360ChartOnMain();
      canvas.baseImageData360 = ctx.getImageData(0, 0, canvas.width, canvas.height);
    } else {
      // Restore base image
      ctx.putImageData(canvas.baseImageData360, 0, 0);
    }

    // Draw highlighted aspect line if there is one
    if (hoveredLine) {
      ctx.beginPath();
      ctx.moveTo(hoveredLine.x1, hoveredLine.y1);
      ctx.lineTo(hoveredLine.x2, hoveredLine.y2);
      ctx.strokeStyle = hoveredLine.color;
      ctx.lineWidth = 4; // Thicker line for highlight
      ctx.globalAlpha = 0.8;
      ctx.stroke();
      ctx.globalAlpha = 1.0;

      // Add a glow effect
      ctx.beginPath();
      ctx.moveTo(hoveredLine.x1, hoveredLine.y1);
      ctx.lineTo(hoveredLine.x2, hoveredLine.y2);
      ctx.strokeStyle = hoveredLine.color;
      ctx.lineWidth = 8;
      ctx.globalAlpha = 0.3;
      ctx.stroke();
      ctx.globalAlpha = 1.0;
    }
  }

  // Function to show 360° aspect interpretation
  function show360AspectInterpretation(aspectLine) {
    // Convert aspect type to the format expected by the interpretation system
    const aspectTypeMap = {
      'conjunction': 'CNJ',
      'victoire-martial': 'VMA',
      'trigone-2': 'TR2',
      'sextile': 'SXT',
      'square': 'SQR',
      'trine': 'TRI',
      'trigone-3': 'TR3',
      'opposition': 'OPP'
    };

    const aspectCode = aspectTypeMap[aspectLine.aspectType];
    if (!aspectCode) {
      console.log('Type d\'aspect non reconnu:', aspectLine.aspectType);
      return;
    }

    // Convert planet names to codes
    const planetCodeMap = {
      'sun': 'SU',
      'moon': 'MO',
      'mercury': 'ME',
      'venus': 'VE',
      'mars': 'MA',
      'jupiter': 'JU',
      'saturn': 'SA',
      'uranus': 'UR',
      'neptune': 'NE'
    };

    const transitPlanetCode = planetCodeMap[aspectLine.transitPlanet];
    const natalPlanetCode = planetCodeMap[aspectLine.natalPlanet];

    if (!transitPlanetCode || !natalPlanetCode) {
      console.log('Planète non reconnue:', aspectLine.transitPlanet, aspectLine.natalPlanet);
      return;
    }

    // Create aspect object similar to 30° mode
    const aspect = {
      transitPlanet: aspectLine.transitPlanet,
      natalPlanet: aspectLine.natalPlanet,
      aspectType: aspectLine.aspectType,
      aspectFrenchName: aspectLine.aspectFrenchName,
      orb: aspectLine.orb,
      transitPosition: aspectLine.transitPosition,
      natalPosition: aspectLine.natalPosition,
      separation: aspectLine.separation
    };

    // Show the same interpretation popup as 30° mode
    showAspectInterpretation(aspect);
  }

  // Function to draw 360° chart on main canvas
  function draw360ChartOnMain() {
    const canvas = document.getElementById('circular-zodiac-canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const outerRadius = Math.min(centerX, centerY) - 40;
    const innerRadius = outerRadius * 0.7;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Use stored positions if available, otherwise calculate
    let birthPositions = window.lastBirthPositions;
    let transitPositions = window.lastTransitPositions;

    if (!birthPositions || !transitPositions) {
      const birthDate = getBirthDate();
      const transitDate = getTransitDate();

      if (!birthDate || !transitDate) {
        ctx.fillStyle = '#666';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Veuillez calculer la carte d\'abord', centerX, centerY);
        return;
      }

      birthPositions = calculatePlanetaryPositions(birthDate);
      transitPositions = calculatePlanetaryPositions(transitDate);
    }

    // Draw zodiac circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#4a90e2';
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#ff0000';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw zodiac signs
    const signs = ['♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓'];
    for (let i = 0; i < 12; i++) {
      const angle = (i * 30 - 90) * Math.PI / 180; // Start from Aries at top
      const signRadius = outerRadius + 20;
      const signX = centerX + signRadius * Math.cos(angle);
      const signY = centerY + signRadius * Math.sin(angle);

      ctx.fillStyle = '#333';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(signs[i], signX, signY);

      // Draw degree markers
      for (let deg = 0; deg < 30; deg += 10) {
        const degAngle = ((i * 30 + deg) - 90) * Math.PI / 180;
        const markerStart = outerRadius - 5;
        const markerEnd = outerRadius + 5;

        const startX = centerX + markerStart * Math.cos(degAngle);
        const startY = centerY + markerStart * Math.sin(degAngle);
        const endX = centerX + markerEnd * Math.cos(degAngle);
        const endY = centerY + markerEnd * Math.sin(degAngle);

        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo(endX, endY);
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 1;
        ctx.stroke();
      }
    }

    // Draw aspects first (behind planets)
    draw360AspectsOnMain(ctx, centerX, centerY, innerRadius * 0.85, outerRadius * 0.92, birthPositions, transitPositions);



    // Draw natal planets (inner circle)
    draw360PlanetsOnMain(ctx, centerX, centerY, innerRadius * 0.85, birthPositions, false);

    // Draw transit planets (outer circle)
    draw360PlanetsOnMain(ctx, centerX, centerY, outerRadius * 0.92, transitPositions, true);
  }

  // Function to draw planets in 360° mode on main canvas
  function draw360PlanetsOnMain(ctx, centerX, centerY, radius, positions, isTransit) {
    const planets = [
      { key: 'sun', name: 'Sun', symbol: '☉', color: isTransit ? '#FF6B35' : '#FFA500' },
      { key: 'moon', name: 'Moon', symbol: '☽', color: isTransit ? '#87CEEB' : '#C0C0C0' },
      { key: 'mercury', name: 'Mercury', symbol: '☿', color: isTransit ? '#32CD32' : '#90EE90' },
      { key: 'venus', name: 'Venus', symbol: '♀', color: isTransit ? '#FF1493' : '#FFB6C1' },
      { key: 'mars', name: 'Mars', symbol: '♂', color: isTransit ? '#DC143C' : '#FF6347' },
      { key: 'jupiter', name: 'Jupiter', symbol: '♃', color: isTransit ? '#4169E1' : '#87CEFA' },
      { key: 'saturn', name: 'Saturn', symbol: '♄', color: isTransit ? '#8B4513' : '#DEB887' },
      { key: 'uranus', name: 'Uranus', symbol: '♅', color: isTransit ? '#00CED1' : '#AFEEEE' },
      { key: 'neptune', name: 'Neptune', symbol: '♆', color: isTransit ? '#4B0082' : '#9370DB' }
    ];

    planets.forEach(planet => {
      const planetData = positions[planet.key];
      if (!planetData) return;

      // Calculate full 360° position
      // planetData.sign is the sign name (string), need to convert to index
      const signNames = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                        'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];
      const signIndex = signNames.indexOf(planetData.sign);

      if (signIndex === -1) return;

      const fullPosition = signIndex * 30 + parseFloat(planetData.degree);
      const angle = (fullPosition - 90) * Math.PI / 180; // Start from Aries at top


      const planetX = centerX + radius * Math.cos(angle);
      const planetY = centerY + radius * Math.sin(angle);

      // Draw white background circle
      ctx.beginPath();
      ctx.arc(planetX, planetY, 12, 0, 2 * Math.PI);
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.fill();

      // Draw planet symbol
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = planet.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planet.symbol, planetX, planetY);

      // Draw circle border
      ctx.beginPath();
      ctx.arc(planetX, planetY, 10, 0, 2 * Math.PI);
      ctx.strokeStyle = planet.color;
      ctx.lineWidth = 1;
      ctx.stroke();
    });
  }

  // Function to draw aspects in 360° mode on main canvas
  function draw360AspectsOnMain(ctx, centerX, centerY, natalRadius, transitRadius, birthPositions, transitPositions) {
    const aspectTypes = [
      { name: 'conjunction', angle: 0, color: '#FF0000', lineWidth: 2, frenchName: 'Conjonction', orb: 2.0 },
      { name: 'victoire-martial', angle: 16, color: '#FF8C00', lineWidth: 1, frenchName: 'Victoire Martial', orb: 2.0 },
      { name: 'trigone-2', angle: 40, color: '#32CD32', lineWidth: 1, frenchName: 'Trigone 2', orb: 2.0 },
      { name: 'sextile', angle: 60, color: '#00AA00', lineWidth: 1, frenchName: 'Sextile', orb: 2.0 },
      { name: 'square', angle: 90, color: '#FF6600', lineWidth: 1, frenchName: 'Carré', orb: 2.0 },
      { name: 'trine', angle: 120, color: '#0066FF', lineWidth: 1, frenchName: 'Trigone', orb: 1.0 },
      { name: 'trigone-3', angle: 162, color: '#4169E1', lineWidth: 1, frenchName: 'Trigone 3', orb: 2.0 },
      { name: 'opposition', angle: 180, color: '#000000', lineWidth: 2, frenchName: 'Opposition', orb: 2.0 }
    ];

    // Clear previous 360° aspect lines data
    if (!window.aspectLines360) {
      window.aspectLines360 = [];
    }
    window.aspectLines360 = [];
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    planets.forEach(transitPlanet => {
      planets.forEach(natalPlanet => {
        const transitData = transitPositions[transitPlanet];
        const natalData = birthPositions[natalPlanet];

        if (!transitData || !natalData) return;

        // Calculate full 360° positions
        const signNames = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                          'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];
        const transitSignIndex = signNames.indexOf(transitData.sign);
        const natalSignIndex = signNames.indexOf(natalData.sign);
        const transitPos = transitSignIndex * 30 + parseFloat(transitData.degree);
        const natalPos = natalSignIndex * 30 + parseFloat(natalData.degree);

        // Calculate separation
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 180) separation = 360 - separation;

        // Check each aspect type
        aspectTypes.forEach(aspectType => {
          const aspectAngle = aspectType.angle;
          const orbDifference = Math.abs(separation - aspectAngle);
          const aspectOrbValue = aspectType.orb || 2.0; // Use specific orb or default to 2.0

          if (orbDifference <= aspectOrbValue) {
            // Calculate positions on circles
            const transitAngle = (transitPos - 90) * Math.PI / 180;
            const natalAngle = (natalPos - 90) * Math.PI / 180;

            const transitX = centerX + (transitRadius - 15) * Math.cos(transitAngle);
            const transitY = centerY + (transitRadius - 15) * Math.sin(transitAngle);
            const natalX = centerX + (natalRadius - 15) * Math.cos(natalAngle);
            const natalY = centerY + (natalRadius - 15) * Math.sin(natalAngle);

            // Store aspect line data for hover detection
            window.aspectLines360.push({
              x1: natalX,
              y1: natalY,
              x2: transitX,
              y2: transitY,
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType.name,
              aspectFrenchName: aspectType.frenchName,
              orb: orbDifference.toFixed(1),
              transitPosition: transitPos.toFixed(1),
              natalPosition: natalPos.toFixed(1),
              separation: separation.toFixed(1),
              color: aspectType.color
            });

            // Draw aspect line
            ctx.beginPath();
            ctx.moveTo(natalX, natalY);
            ctx.lineTo(transitX, transitY);
            ctx.strokeStyle = aspectType.color;
            ctx.lineWidth = aspectType.lineWidth;
            ctx.stroke();
          }
        });
      });
    });
  }



  // Function to initialize planet position navigation
  function initPlanetPositionNavigation() {
    // Add event listeners for navigation buttons
    if (prevPositionBtn) {
      prevPositionBtn.addEventListener('click', function() {
        navigateToPreviousPosition();
      });
    }

    if (nextPositionBtn) {
      nextPositionBtn.addEventListener('click', function() {
        navigateToNextPosition();
      });
    }
  }

  // Function to handle planet click for position navigation
  function handlePlanetClick(planetName, planetPosition) {
    selectedPlanet = planetName;
    selectedPlanetPosition = planetPosition;

    // Show the navigation section
    if (planetPositionNavigation) {
      planetPositionNavigation.style.display = 'block';
    }

    // Update the title and description
    if (selectedPlanetTitle) {
      selectedPlanetTitle.textContent = `Navigation - ${getPlanetFrenchName(planetName)}`;
    }

    if (planetNavDescription) {
      planetNavDescription.textContent = `Naviguez vers les dates où ${getPlanetFrenchName(planetName)} sera à la même position (${planetPosition.toFixed(1)}°).`;
    }

    // Show planet details
    if (planetNavDetails) {
      planetNavDetails.style.display = 'block';
    }

    if (currentPlanetPosition) {
      currentPlanetPosition.textContent = `${planetPosition.toFixed(1)}°`;
    }

    // Enable navigation buttons
    if (prevPositionBtn) {
      prevPositionBtn.disabled = false;
    }
    if (nextPositionBtn) {
      nextPositionBtn.disabled = false;
    }

    // Generate and display the dates table
    generatePlanetDatesTable(planetName, planetPosition);
  }

  // Function to navigate to previous position
  function navigateToPreviousPosition() {
    if (!selectedPlanet || selectedPlanetPosition === null || !window.planetDatesTable) return;

    const currentDate = getCurrentTransitDate();

    // Find the previous date from the table
    const previousDate = findPreviousDateFromTable(currentDate);

    if (previousDate) {
      // Update transit date inputs
      updateTransitDateToSpecific(previousDate);

      // Switch to manual mode if in auto mode
      if (isAutoTransitMode) {
        toggleTransitMode();
      }

      // Recalculate chart
      calculateChart();

      // Update day comments display
      if (window.updateDayCommentsDisplay) {
        window.updateDayCommentsDisplay().catch(console.error);
      }

      // Show success message
      showSaveMessage(`Date précédente trouvée: ${previousDate.toLocaleDateString('fr-FR')}`);
    } else {
      showSaveMessage('Aucune date précédente trouvée dans le tableau.');
    }
  }

  // Function to navigate to next position
  function navigateToNextPosition() {
    if (!selectedPlanet || selectedPlanetPosition === null || !window.planetDatesTable) return;

    const currentDate = getCurrentTransitDate();

    // Find the next date from the table
    const nextDate = findNextDateFromTable(currentDate);

    if (nextDate) {
      // Update transit date inputs
      updateTransitDateToSpecific(nextDate);

      // Switch to manual mode if in auto mode
      if (isAutoTransitMode) {
        toggleTransitMode();
      }

      // Recalculate chart
      calculateChart();

      // Update day comments display
      if (window.updateDayCommentsDisplay) {
        window.updateDayCommentsDisplay().catch(console.error);
      }

      // Show success message
      showSaveMessage(`Date suivante trouvée: ${nextDate.toLocaleDateString('fr-FR')}`);
    } else {
      showSaveMessage('Aucune date suivante trouvée dans le tableau.');
    }
  }

  // Function to find previous date from the table
  function findPreviousDateFromTable(currentDate) {
    if (!window.planetDatesTable || window.planetDatesTable.length === 0) return null;

    // Sort dates and find the one just before current date
    const sortedDates = window.planetDatesTable
      .map(dateInfo => dateInfo.date)
      .sort((a, b) => a.getTime() - b.getTime());

    // Find the largest date that is smaller than current date
    for (let i = sortedDates.length - 1; i >= 0; i--) {
      if (sortedDates[i].getTime() < currentDate.getTime()) {
        return sortedDates[i];
      }
    }

    return null;
  }

  // Function to find next date from the table
  function findNextDateFromTable(currentDate) {
    if (!window.planetDatesTable || window.planetDatesTable.length === 0) return null;

    // Sort dates and find the one just after current date
    const sortedDates = window.planetDatesTable
      .map(dateInfo => dateInfo.date)
      .sort((a, b) => a.getTime() - b.getTime());

    // Find the smallest date that is larger than current date
    for (let i = 0; i < sortedDates.length; i++) {
      if (sortedDates[i].getTime() > currentDate.getTime()) {
        return sortedDates[i];
      }
    }

    return null;
  }

  // Function to get current transit date
  function getCurrentTransitDate() {
    if (isAutoTransitMode) {
      return new Date();
    } else {
      const date = new Date(transitDateInput.value);
      date.setHours(
        parseInt(transitHourInput.value) || 0,
        parseInt(transitMinuteInput.value) || 0,
        0
      );
      return date;
    }
  }

  // Function to find date when planet is at target position
  function findPlanetPositionDate(planetName, targetPosition, startDate, direction) {
    const tolerance = 0.1; // Very small tolerance for near-exact positions
    const maxDays = 2000; // Maximum days to search (increased for 6 months range)
    let currentDate = new Date(startDate);
    let found = false;

    // Move one day in the search direction to avoid finding the same date
    currentDate.setDate(currentDate.getDate() + direction);

    for (let days = 0; days < maxDays; days++) {
      const positions = calculatePlanetaryPositions(currentDate);
      const planetData = positions[planetName];

      if (planetData) {
        // For proportional mode (30°), use only degree within sign
        const currentPosition = parseFloat(planetData.degree);
        const targetPos = targetPosition;

        // Check if we're close to the target position
        let difference = Math.abs(currentPosition - targetPos);

        // Handle wrap-around at 30° (not 360°)
        if (difference > 15) {
          difference = 30 - difference;
        }

        if (difference <= tolerance) {
          found = true;
          break;
        }
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + direction);
    }

    if (found) {
      // Update transit date inputs
      updateTransitDateToSpecific(currentDate);

      // Switch to manual mode if in auto mode
      if (isAutoTransitMode) {
        toggleTransitMode();
      }

      // Recalculate chart
      calculateChart();

      // Show success message
      const directionText = direction > 0 ? 'suivante' : 'précédente';
      showSaveMessage(`Date ${directionText} trouvée: ${currentDate.toLocaleDateString('fr-FR')}`);
    } else {
      // Show error message
      const directionText = direction > 0 ? 'suivante' : 'précédente';
      showSaveMessage(`Aucune date ${directionText} trouvée dans les ${maxDays} prochains jours.`);
    }
  }

  // Function to update transit date to specific date
  function updateTransitDateToSpecific(date) {
    transitDateInput.value = date.toISOString().split('T')[0];
    transitHourInput.value = date.getHours();
    transitMinuteInput.value = date.getMinutes();

    const transitSecondInput = document.getElementById('transit-second');
    if (transitSecondInput) {
      transitSecondInput.value = date.getSeconds();
    }
  }

  // Function to setup click handlers for transit planets
  function setupTransitPlanetClickHandlers(canvas, centerX, centerY, radius, transitPositions) {
    // Remove existing event listener if any
    canvas.removeEventListener('click', canvas.planetClickHandler);

    // Create new event listener
    canvas.planetClickHandler = function(event) {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Check if click is on a transit planet
      const planets = [
        { key: 'sun', name: 'Sun' },
        { key: 'moon', name: 'Moon' },
        { key: 'mercury', name: 'Mercury' },
        { key: 'venus', name: 'Venus' },
        { key: 'mars', name: 'Mars' },
        { key: 'jupiter', name: 'Jupiter' },
        { key: 'saturn', name: 'Saturn' },
        { key: 'uranus', name: 'Uranus' },
        { key: 'neptune', name: 'Neptune' }
      ];

      planets.forEach(planet => {
        const planetData = transitPositions[planet.key];
        if (!planetData) return;

        // Calculate planet position
        const proportionalPosition = getProportionalPosition(planetData);
        const angle = ((proportionalPosition * 12) - 90) * Math.PI / 180;
        const planetX = centerX + radius * Math.cos(angle);
        const planetY = centerY + radius * Math.sin(angle);

        // Check if click is within planet area (15px radius)
        const distance = Math.sqrt((x - planetX) ** 2 + (y - planetY) ** 2);
        if (distance <= 15) {
          // For 30° mode, use only the degree within the sign (0-30°)
          const proportionalPosition = parseFloat(planetData.degree);

          // Call the planet click handler
          handlePlanetClick(planet.key, proportionalPosition);
        }
      });
    };

    // Add the event listener
    canvas.addEventListener('click', canvas.planetClickHandler);
  }

  // Function to generate and display the planet dates table
  function generatePlanetDatesTable(planetName, targetPosition) {
    const tableContainer = document.getElementById('planet-dates-table-container');
    const tableBody = document.getElementById('planet-dates-tbody');
    const loadingDiv = document.getElementById('table-loading');

    if (!tableContainer || !tableBody || !loadingDiv) return;

    // Show the table container and loading
    tableContainer.style.display = 'block';
    loadingDiv.style.display = 'block';
    tableBody.innerHTML = '';

    // Calculate proportional position (0-30°)
    const proportionalPosition = targetPosition % 30;

    // Get current date
    const currentDate = getCurrentTransitDate();
    const dates = [];

    // Calculate dates in background to avoid blocking UI
    setTimeout(() => {
      try {
        // Search for past dates with extended periods for slow planets
        let searchRangePast;
        if (planetName === 'sun' || planetName === 'moon' || planetName === 'mercury' || planetName === 'venus') {
          searchRangePast = -365; // 1 year for fast planets
        } else if (planetName === 'mars') {
          searchRangePast = -730; // 2 years for Mars
        } else {
          searchRangePast = -1825; // 5 years for slow planets (Jupiter, Saturn, Uranus, Neptune)
        }
        const pastDates = findPlanetPositionDatesInRange(planetName, proportionalPosition, currentDate, searchRangePast, -1);

        // Search for future dates with extended periods for slow planets
        let searchRangeFuture;
        if (planetName === 'sun' || planetName === 'moon' || planetName === 'mercury' || planetName === 'venus') {
          searchRangeFuture = 365; // 1 year for fast planets
        } else if (planetName === 'mars') {
          searchRangeFuture = 730; // 2 years for Mars
        } else {
          searchRangeFuture = 1825; // 5 years for slow planets (Jupiter, Saturn, Uranus, Neptune)
        }
        const futureDates = findPlanetPositionDatesInRange(planetName, proportionalPosition, currentDate, 1, searchRangeFuture);

        // Combine and sort dates
        const allDates = [...pastDates, ...futureDates];
        allDates.sort((a, b) => a.date.getTime() - b.date.getTime());

        // Group dates by cycles and keep only the best match per cycle
        const cycleFilteredDates = filterDatesByCycles(allDates, planetName);

        // Hide loading
        loadingDiv.style.display = 'none';

        // Store dates globally for navigation
        window.planetDatesTable = cycleFilteredDates;

        // Populate table
        populateDatesTable(cycleFilteredDates, currentDate, proportionalPosition);

      } catch (error) {
        console.error('Error generating dates table:', error);
        loadingDiv.innerHTML = '<p>Erreur lors du calcul des dates.</p>';
      }
    }, 100);
  }

  // Function to filter dates by astrological cycles - keep only one date per cycle
  function filterDatesByCycles(allDates, planetName) {
    if (allDates.length === 0) return allDates;

    // Define cycle thresholds based on planet type
    const cycleThresholds = {
      // Fast planets: shorter time threshold, smaller position tolerance
      'sun': { timeDays: 30, positionTolerance: 2.0 },
      'moon': { timeDays: 7, positionTolerance: 3.0 },
      'mercury': { timeDays: 25, positionTolerance: 2.5 },
      'venus': { timeDays: 35, positionTolerance: 2.5 },
      'mars': { timeDays: 60, positionTolerance: 2.0 },
      // Slow planets: longer time threshold, smaller position tolerance
      'jupiter': { timeDays: 120, positionTolerance: 1.5 },
      'saturn': { timeDays: 150, positionTolerance: 1.0 },
      'uranus': { timeDays: 300, positionTolerance: 0.8 },
      'neptune': { timeDays: 400, positionTolerance: 0.8 }
    };

    const threshold = cycleThresholds[planetName] || { timeDays: 60, positionTolerance: 2.0 };
    const timeThresholdMs = threshold.timeDays * 24 * 60 * 60 * 1000;
    const positionTolerance = threshold.positionTolerance;

    const filteredDates = [];
    const usedDates = new Set();

    // Sort dates by time
    const sortedDates = [...allDates].sort((a, b) => a.date.getTime() - b.date.getTime());

    for (let i = 0; i < sortedDates.length; i++) {
      const currentDate = sortedDates[i];

      if (usedDates.has(i)) continue;

      // Find all dates in the same cycle (close in time AND position)
      const cycleGroup = [currentDate];
      const cycleIndices = [i];

      for (let j = i + 1; j < sortedDates.length; j++) {
        if (usedDates.has(j)) continue;

        const otherDate = sortedDates[j];
        const timeDiff = Math.abs(otherDate.date.getTime() - currentDate.date.getTime());
        const positionDiff = Math.abs(otherDate.position - currentDate.position);

        // Handle wrap-around at 30°
        const adjustedPositionDiff = positionDiff > 15 ? 30 - positionDiff : positionDiff;

        // If dates are close in time AND position, they belong to the same cycle
        if (timeDiff <= timeThresholdMs && adjustedPositionDiff <= positionTolerance) {
          cycleGroup.push(otherDate);
          cycleIndices.push(j);
        }
      }

      // Mark all dates in this cycle as used
      cycleIndices.forEach(index => usedDates.add(index));

      // Find the best date in this cycle (closest position to target)
      // We'll use the target position from the first date as reference
      const targetPos = currentDate.position;
      let bestDate = cycleGroup[0];
      let bestPositionDiff = 0; // First date is the reference

      for (const dateInfo of cycleGroup) {
        const positionDiff = Math.abs(dateInfo.position - targetPos);
        const adjustedDiff = positionDiff > 15 ? 30 - positionDiff : positionDiff;

        if (adjustedDiff < bestPositionDiff || bestDate === cycleGroup[0]) {
          bestDate = dateInfo;
          bestPositionDiff = adjustedDiff;
        }
      }

      filteredDates.push(bestDate);
    }

    console.log(`Filtered ${allDates.length} dates to ${filteredDates.length} cycles for ${planetName}`);
    return filteredDates.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  // Function to find planet position dates in a range
  function findPlanetPositionDatesInRange(planetName, targetPosition, startDate, startDayOffset, endDayOffset) {
    const dates = [];
    // Adjust tolerance based on planet speed - faster planets need larger tolerance
    const tolerance = (planetName === 'sun' || planetName === 'moon') ? 1.0 :
                     (planetName === 'mercury' || planetName === 'venus' || planetName === 'mars') ? 0.8 : 0.5;

    // For past dates: startDayOffset is negative (e.g., -365), endDayOffset is negative (e.g., -1)
    // For future dates: startDayOffset is positive (e.g., 1), endDayOffset is positive (e.g., 365)

    const searchStartDate = new Date(startDate);
    searchStartDate.setDate(searchStartDate.getDate() + startDayOffset);

    const searchEndDate = new Date(startDate);
    searchEndDate.setDate(searchEndDate.getDate() + endDayOffset);

    const direction = startDayOffset < endDayOffset ? 1 : -1;
    const totalDays = Math.abs(endDayOffset - startDayOffset);

    console.log(`Searching for ${planetName} at position ${targetPosition}° over ${totalDays} days`);
    console.log(`Start date: ${searchStartDate.toLocaleDateString()}, End date: ${searchEndDate.toLocaleDateString()}, Direction: ${direction}`);

    const searchDate = new Date(searchStartDate);

    for (let day = 0; day < totalDays; day++) {
      const positions = calculatePlanetaryPositions(searchDate);
      const planetData = positions[planetName];

      if (planetData) {
        const currentProportionalPosition = parseFloat(planetData.degree);

        // Check if position matches (considering wrap-around at 30°)
        let difference = Math.abs(currentProportionalPosition - targetPosition);
        if (difference > 15) difference = 30 - difference;

        // Debug log every 30 days
        if (day % 30 === 0) {
          console.log(`Day ${day}: ${searchDate.toLocaleDateString()}, ${planetName} at ${currentProportionalPosition.toFixed(1)}°, diff: ${difference.toFixed(1)}°`);
        }

        if (difference <= tolerance) {
          console.log(`MATCH FOUND: ${searchDate.toLocaleDateString()}, ${planetName} at ${currentProportionalPosition.toFixed(1)}°`);

          // Check if we already have a date very close to this one (within 2 days)
          const isDuplicate = dates.some(existingDate => {
            const timeDiff = Math.abs(existingDate.date.getTime() - searchDate.getTime());
            return timeDiff < (2 * 24 * 60 * 60 * 1000); // 2 days in milliseconds
          });

          if (!isDuplicate) {
            // For very close matches, try to find the exact time within the day
            const exactTime = findExactTimeForPosition(planetName, targetPosition, searchDate);
            dates.push({
              date: exactTime || new Date(searchDate),
              position: currentProportionalPosition, // This is already the proportional position (0-30°)
              sign: planetData.sign
            });
          }
        }
      }

      searchDate.setDate(searchDate.getDate() + direction);
    }

    console.log(`Found ${dates.length} dates for ${planetName} at ${targetPosition}°`);

    return dates;
  }

  // Function to find exact time within a day for a target position
  function findExactTimeForPosition(planetName, targetPosition, baseDate) {
    const tolerance = 0.05; // Very small tolerance for exact time

    // Search through the day in 1-hour intervals
    for (let hour = 0; hour < 24; hour++) {
      const testDate = new Date(baseDate);
      testDate.setHours(hour, 0, 0, 0);

      const positions = calculatePlanetaryPositions(testDate);
      const planetData = positions[planetName];

      if (planetData) {
        const currentPosition = parseFloat(planetData.degree);
        let difference = Math.abs(currentPosition - targetPosition);
        if (difference > 15) difference = 30 - difference;

        if (difference <= tolerance) {
          // Found a close match, now search in 10-minute intervals around this hour
          for (let minute = 0; minute < 60; minute += 10) {
            const preciseDate = new Date(baseDate);
            preciseDate.setHours(hour, minute, 0, 0);

            const precisePositions = calculatePlanetaryPositions(preciseDate);
            const precisePlanetData = precisePositions[planetName];

            if (precisePlanetData) {
              const precisePosition = parseFloat(precisePlanetData.degree);
              let preciseDifference = Math.abs(precisePosition - targetPosition);
              if (preciseDifference > 15) preciseDifference = 30 - preciseDifference;

              if (preciseDifference <= tolerance) {
                return preciseDate;
              }
            }
          }

          // If no precise match found, return the hour match
          return testDate;
        }
      }
    }

    return null;
  }

  // Function to populate the dates table
  function populateDatesTable(dates, currentDate, targetPosition) {
    const tableBody = document.getElementById('planet-dates-tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    if (dates.length === 0) {
      const row = tableBody.insertRow();
      row.innerHTML = '<td colspan="5" style="text-align: center; font-style: italic; color: #6c757d;">Aucune date trouvée dans la période</td>';
      return;
    }

    dates.forEach(dateInfo => {
      const row = tableBody.insertRow();
      const isPast = dateInfo.date < currentDate;
      const isCurrent = Math.abs(dateInfo.date.getTime() - currentDate.getTime()) < 24 * 60 * 60 * 1000; // Within 24 hours

      // Type column
      const typeCell = row.insertCell(0);
      typeCell.className = isCurrent ? 'date-type-current' : (isPast ? 'date-type-past' : 'date-type-future');
      typeCell.textContent = isCurrent ? 'Actuel' : (isPast ? 'Passé' : 'Futur');

      // Date column
      const dateCell = row.insertCell(1);
      dateCell.className = 'planet-date-cell';
      dateCell.textContent = dateInfo.date.toLocaleDateString('fr-FR');

      // Time column
      const timeCell = row.insertCell(2);
      timeCell.className = 'planet-time-cell';
      timeCell.textContent = dateInfo.date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Position column
      const positionCell = row.insertCell(3);
      positionCell.className = 'planet-position-cell';
      positionCell.textContent = `${dateInfo.position.toFixed(1)}°`;

      // Action column
      const actionCell = row.insertCell(4);
      const gotoBtn = document.createElement('button');
      gotoBtn.className = 'goto-date-btn';
      gotoBtn.textContent = 'Aller';
      gotoBtn.disabled = isCurrent;

      if (!isCurrent) {
        gotoBtn.onclick = () => {
          updateTransitDateToSpecific(dateInfo.date);
          // Recalculate chart
          const calculateBtn = document.getElementById('calculate-btn');
          if (calculateBtn) {
            calculateBtn.click();
          }
          // Update day comments display
          if (window.updateDayCommentsDisplay) {
            window.updateDayCommentsDisplay().catch(console.error);
          }
        };
      }

      actionCell.appendChild(gotoBtn);
    });
  }

  // Make handlePlanetClick available globally for the zodiac drawing function
  window.handlePlanetClick = handlePlanetClick;



  // Function to setup click handlers for aspect lines
  function setupAspectLineClickHandlers(canvas) {
    // Remove existing event listeners if any
    if (canvas.aspectLineClickHandler) {
      canvas.removeEventListener('click', canvas.aspectLineClickHandler);
    }
    if (canvas.aspectLineHoverHandler) {
      canvas.removeEventListener('mousemove', canvas.aspectLineHoverHandler);
    }
    if (canvas.aspectLineLeaveHandler) {
      canvas.removeEventListener('mouseleave', canvas.aspectLineLeaveHandler);
    }

    // Store the currently hovered aspect line
    let hoveredAspectLine = null;

    // Create hover event listener for aspect lines
    canvas.aspectLineHoverHandler = function(event) {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      let foundHoveredLine = null;

      // Check if mouse is near any aspect line
      if (window.aspectLines) {
        for (let aspectLine of window.aspectLines) {
          if (isPointNearLine(x, y, aspectLine.x1, aspectLine.y1, aspectLine.x2, aspectLine.y2, 10)) {
            foundHoveredLine = aspectLine;
            break;
          }
        }
      }

      // If hover state changed, redraw
      if (foundHoveredLine !== hoveredAspectLine) {
        hoveredAspectLine = foundHoveredLine;

        // Change cursor style
        canvas.style.cursor = hoveredAspectLine ? 'pointer' : 'default';

        // Redraw aspects with highlight
        redrawAspectsWithHighlight(canvas, hoveredAspectLine);
      }
    };

    // Create mouse leave event listener
    canvas.aspectLineLeaveHandler = function(event) {
      if (hoveredAspectLine) {
        hoveredAspectLine = null;
        canvas.style.cursor = 'default';
        redrawAspectsWithHighlight(canvas, null);
      }
    };

    // Create click event listener for aspect lines
    canvas.aspectLineClickHandler = function(event) {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Check if click is near any aspect line
      if (window.aspectLines) {
        for (let aspectLine of window.aspectLines) {
          if (isPointNearLine(x, y, aspectLine.x1, aspectLine.y1, aspectLine.x2, aspectLine.y2, 10)) {
            // Show interpretation popup for this aspect
            showAspectInterpretation(aspectLine);
            event.stopPropagation(); // Prevent other click handlers
            return;
          }
        }
      }
    };

    // Add the event listeners
    canvas.addEventListener('click', canvas.aspectLineClickHandler);
    canvas.addEventListener('mousemove', canvas.aspectLineHoverHandler);
    canvas.addEventListener('mouseleave', canvas.aspectLineLeaveHandler);
  }

  // Function to redraw only the aspect lines with highlight
  function redrawAspectsWithHighlight(canvas, hoveredLine) {
    if (!window.aspectLines || window.aspectLines.length === 0) return;

    const ctx = canvas.getContext('2d');

    // Store the base canvas if not already stored
    if (!canvas.baseImageData && !hoveredLine) {
      canvas.baseImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      return;
    }

    // If we have a base image and we're clearing highlight, restore it
    if (canvas.baseImageData && !hoveredLine) {
      ctx.putImageData(canvas.baseImageData, 0, 0);
      return;
    }

    // If we have a base image and we're highlighting, restore base first then add highlight
    if (canvas.baseImageData && hoveredLine) {
      ctx.putImageData(canvas.baseImageData, 0, 0);

      // Now draw the highlight effect for the hovered line
      ctx.save();

      // Draw glow effect (multiple lines with decreasing opacity)
      for (let i = 6; i >= 1; i--) {
        ctx.beginPath();
        ctx.moveTo(hoveredLine.x1, hoveredLine.y1);
        ctx.lineTo(hoveredLine.x2, hoveredLine.y2);
        ctx.strokeStyle = `rgba(255, 255, 0, ${0.15 * (7 - i)})`; // Yellow glow
        ctx.lineWidth = i * 1.5;
        ctx.stroke();
      }

      // Draw the main highlighted line
      ctx.beginPath();
      ctx.moveTo(hoveredLine.x1, hoveredLine.y1);
      ctx.lineTo(hoveredLine.x2, hoveredLine.y2);

      // Get original color but make it brighter
      const aspectType = getAspectTypeData(hoveredLine.aspectType);
      ctx.strokeStyle = aspectType.color;
      ctx.lineWidth = aspectType.lineWidth + 1;
      ctx.stroke();

      ctx.restore();
      return;
    }

    // Fallback: redraw everything if no base image
    if (window.birthPositions && window.transitPositions) {
      drawCircularProportionalZodiac(window.birthPositions, window.transitPositions);
    }
  }

  // Function to get aspect type data
  function getAspectTypeData(aspectTypeName) {
    const aspectTypes = {
      'conjunction': { color: '#FF0000', lineWidth: 2 },
      'sextile': { color: '#00AA00', lineWidth: 1 },
      'square': { color: '#FF6600', lineWidth: 1 },
      'trine': { color: '#0066FF', lineWidth: 1 },
      'opposition': { color: '#000000', lineWidth: 2 }
    };
    return aspectTypes[aspectTypeName] || { color: '#000000', lineWidth: 1 };
  }

  // Function to check if a point is near a line
  function isPointNearLine(px, py, x1, y1, x2, y2, threshold) {
    // Calculate distance from point to line segment
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) {
      // Line segment is a point
      return Math.sqrt(A * A + B * B) <= threshold;
    }

    let param = dot / lenSq;

    let xx, yy;

    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy) <= threshold;
  }

  // Initialize monthly aspects table functionality
  function initMonthlyAspectsTable() {
    // Initialize tabs
    const tabButtons = document.querySelectorAll('.monthly-tab-btn');
    const tabContents = document.querySelectorAll('.monthly-tab-content');

    tabButtons.forEach(button => {
      button.addEventListener('click', function() {
        const targetTab = this.getAttribute('data-tab');

        // Remove active class from all tabs and contents
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        // Add active class to clicked tab and corresponding content
        this.classList.add('active');
        document.getElementById(targetTab).classList.add('active');

        // Generate table for the selected tab
        generateMonthlyTable(targetTab);
      });
    });

    // Initialize monthly filters for each tab
    initMonthlyFilters();

    // Initialize 360° mode toggle
    initMonthly360ModeToggle();

    // Generate initial table for current month
    generateMonthlyTable('current-month');
  }

  // Initialize monthly filters
  function initMonthlyFilters() {
    // Initialize filters for each tab
    const tabs = ['current-month', 'next-month', 'prev-month'];

    tabs.forEach(tabId => {
      const suffix = tabId === 'current-month' ? '' : `-${tabId.split('-')[0]}`;

      // Orb filter
      const orbFilter = document.getElementById(`monthly-orb-filter${suffix}`);
      const orbValue = document.getElementById(`monthly-orb-value${suffix}`);
      const applyBtn = document.getElementById(`monthly-apply-filter${suffix}`);

      if (orbFilter && orbValue) {
        orbFilter.addEventListener('input', function() {
          orbValue.textContent = this.value + '°';
        });
      }

      if (applyBtn) {
        applyBtn.addEventListener('click', function() {
          generateMonthlyTable(tabId);
        });
      }
    });
  }

  // Get selected aspect types for a tab
  function getSelectedAspectTypes(tabId) {
    const suffix = tabId === 'current-month' ? '' : `-${tabId.split('-')[0]}`;
    const checkboxes = document.querySelectorAll(`input[name="monthly-aspect-type${suffix}"]:checked`);
    return Array.from(checkboxes).map(cb => cb.value);
  }

  // Get selected orb for a tab
  function getSelectedOrb(tabId) {
    const suffix = tabId === 'current-month' ? '' : `-${tabId.split('-')[0]}`;
    const orbFilter = document.getElementById(`monthly-orb-filter${suffix}`);
    return orbFilter ? parseFloat(orbFilter.value) : 0.5;
  }

  // Function to generate monthly table
  function generateMonthlyTable(tabId) {
    const currentDate = new Date();
    let targetDate;

    // Determine which month to display
    switch(tabId) {
      case 'current-month':
        targetDate = new Date(currentDate);
        break;
      case 'next-month':
        targetDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        break;
      case 'prev-month':
        targetDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
        break;
      default:
        targetDate = new Date(currentDate);
    }

    // Update title
    const titleElement = document.getElementById(`${tabId}-title`);
    if (titleElement) {
      const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                         'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
      titleElement.textContent = `Aspects Transit-Natal - ${monthNames[targetDate.getMonth()]} ${targetDate.getFullYear()}`;
    }

    // Generate table structure
    generateTableStructure(tabId, targetDate);

    // Calculate and populate aspects data
    calculateMonthlyAspects(tabId, targetDate);

    // Apply color coding after table generation
    setTimeout(() => applySummaryColorCoding(tabId), 200);
  }

  // Function to generate table structure (headers and planet rows)
  function generateTableStructure(tabId, targetDate) {
    const headerRow = document.getElementById(`${tabId}-days-header`);
    const tbody = document.getElementById(`${tabId}-body`);

    if (!headerRow || !tbody) return;

    // Clear existing content
    headerRow.innerHTML = '';
    tbody.innerHTML = '';

    // Get number of days in month
    const year = targetDate.getFullYear();
    const month = targetDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Create header row with days
    const planetHeader = document.createElement('th');
    planetHeader.textContent = 'Planète';
    headerRow.appendChild(planetHeader);

    for (let day = 1; day <= daysInMonth; day++) {
      const dayHeader = document.createElement('th');
      dayHeader.textContent = day.toString();
      dayHeader.className = 'day-header day-header-clickable';
      dayHeader.setAttribute('data-day', day);
      dayHeader.setAttribute('data-month', month);
      dayHeader.setAttribute('data-year', year);

      // Highlight current day if it's the current month
      const currentDate = new Date();
      if (year === currentDate.getFullYear() &&
          month === currentDate.getMonth() &&
          day === currentDate.getDate()) {
        dayHeader.classList.add('current-day');
      }

      // Add click handler to day header
      dayHeader.addEventListener('click', function() {
        const clickedDay = parseInt(this.getAttribute('data-day'));
        const clickedMonth = parseInt(this.getAttribute('data-month'));
        const clickedYear = parseInt(this.getAttribute('data-year'));

        navigateToDate(clickedYear, clickedMonth, clickedDay);
      });

      headerRow.appendChild(dayHeader);
    }

    // Create special summary rows first (editable by user, sauf pour le mois suivant)
    const isNextMonth = tabId === 'next-month';
    const summaryRows = [
      { key: 'general-positivity', name: 'Degré de positivité générale (psychique, physique)', isEditable: !isNextMonth },
      { key: 'event-importance', name: 'Niveaux Importance des événements', isEditable: !isNextMonth },
      { key: 'surprise-level', name: 'Degré de surprise', isEditable: !isNextMonth }
    ];

    summaryRows.forEach(summaryRow => {
      const row = tbody.insertRow();

      // Summary row name cell
      const nameCell = row.insertCell();
      nameCell.textContent = summaryRow.name;
      nameCell.className = 'summary-row-name-cell';

      // Day cells for summary rows (editable ou non selon le mois)
      for (let day = 1; day <= daysInMonth; day++) {
        const dayCell = row.insertCell();
        dayCell.className = summaryRow.isEditable ? 'day-cell summary-editable' : 'day-cell summary-predicted';
        dayCell.setAttribute('data-summary-type', summaryRow.key);
        dayCell.setAttribute('data-day', day);
        dayCell.setAttribute('data-month', month);
        dayCell.setAttribute('data-year', year);
        dayCell.setAttribute('contenteditable', summaryRow.isEditable ? 'true' : 'false');
        dayCell.textContent = '-';

        // Ajouter un indicateur pour les prédictions
        if (!summaryRow.isEditable) {
          dayCell.title = 'Prédiction automatique basée sur les patterns du mois actuel';
        }

        // Add input validation and save functionality
        dayCell.addEventListener('blur', function() {
          const value = this.textContent.trim();
          // Validate input (should be a number or -)
          if (value !== '-' && isNaN(value)) {
            this.textContent = '-';
          }
          // Save to local storage
          saveSummaryValue(summaryRow.key, year, month, day, this.textContent);
          // Apply color coding after value change
          setTimeout(() => applySummaryColorCoding(tabId), 50);
        });

        // Load saved value asynchronously
        getSummaryValue(summaryRow.key, year, month, day).then(savedValue => {
          if (savedValue !== null) {
            dayCell.textContent = savedValue;
          }
          // Apply color coding after loading all values
          setTimeout(() => applySummaryColorCoding(tabId), 100);
        });
      }
    });

    // Create planet rows
    const planets = [
      { key: 'sun', name: 'Soleil' },
      { key: 'moon', name: 'Lune' },
      { key: 'mercury', name: 'Mercure' },
      { key: 'venus', name: 'Vénus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    planets.forEach(planet => {
      const row = tbody.insertRow();

      // Planet name cell
      const planetCell = row.insertCell();
      planetCell.textContent = planet.name;
      planetCell.className = 'planet-name-cell';

      // Day cells for planets (clickable to show aspect details)
      for (let day = 1; day <= daysInMonth; day++) {
        const dayCell = row.insertCell();
        dayCell.className = 'day-cell aspect-count aspect-detail-clickable';
        dayCell.setAttribute('data-planet', planet.key);
        dayCell.setAttribute('data-day', day);
        dayCell.setAttribute('data-month', month);
        dayCell.setAttribute('data-year', year);
        dayCell.textContent = '-';

        // Add click handler to show aspect details
        dayCell.addEventListener('click', function() {
          const clickedDay = parseInt(this.getAttribute('data-day'));
          const clickedMonth = parseInt(this.getAttribute('data-month'));
          const clickedYear = parseInt(this.getAttribute('data-year'));
          const planetKey = this.getAttribute('data-planet');

          showDailyAspectsModal(planetKey, clickedYear, clickedMonth, clickedDay);
        });
      }
    });

    // Add sector changes section for slow planets
    addSectorChangesSection(tbody, targetDate);
  }

  // Function to calculate monthly aspects
  function calculateMonthlyAspects(tabId, targetDate) {
    const tbody = document.getElementById(`${tabId}-body`);
    if (!tbody) return;

    const year = targetDate.getFullYear();
    const month = targetDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Get filter settings
    const selectedAspectTypes = getSelectedAspectTypes(tabId);
    const selectedOrb = getSelectedOrb(tabId);

    // Get birth positions once
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );
    const birthPositions = calculatePlanetaryPositions(birthDate);

    // Calculate aspects for each day
    for (let day = 1; day <= daysInMonth; day++) {
      const transitDate = new Date(year, month, day, 12, 0, 0); // Noon for each day
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Calculate aspects for this day with filtered types and orb
      const dayAspects = isMonthlyTable360Mode ?
        calculate360DegreeAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes) :
        calculateProportionalAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes);

      // Count aspects per natal planet
      const aspectCounts = {};
      const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

      planets.forEach(planet => {
        aspectCounts[planet] = dayAspects.filter(aspect => aspect.natalPlanet === planet).length;
      });

      // Summary values are now editable by user, no automatic calculation needed

      // Update planet aspect count cells
      planets.forEach(planet => {
        const cell = tbody.querySelector(`[data-planet="${planet}"][data-day="${day}"]`);
        if (cell) {
          const count = aspectCounts[planet];
          if (count > 0) {
            cell.textContent = count.toString();

            // Add color coding based on count
            cell.classList.remove('high-count', 'medium-count', 'low-count', 'empty-cell');
            if (count >= 3) {
              cell.classList.add('high-count');
            } else if (count >= 2) {
              cell.classList.add('medium-count');
            } else {
              cell.classList.add('low-count');
            }
          } else {
            cell.textContent = '-';
            cell.classList.add('empty-cell');
          }
        }
      });
    }
  }

  // Function to navigate to a specific date
  function navigateToDate(year, month, day) {
    // Create date object
    const targetDate = new Date(year, month, day, 12, 0, 0);

    // Update transit date inputs
    updateTransitDateToSpecific(targetDate);

    // Switch to manual mode if in auto mode
    if (isAutoTransitMode) {
      toggleTransitMode();
    }

    // Recalculate chart
    calculateChart();

    // Update day comments display
    if (window.updateDayCommentsDisplay) {
      window.updateDayCommentsDisplay().catch(console.error);
    }

    // Show success message
    showSaveMessage(`Navigation vers le ${day}/${month + 1}/${year}`);
  }

  // Function to save summary value to local storage
  function saveSummaryValue(summaryType, year, month, day, value) {
    const key = `summary_${summaryType}_${year}_${month}_${day}`;
    chrome.storage.local.set({ [key]: value });
  }

  // Function to get summary value from local storage
  function getSummaryValue(summaryType, year, month, day) {
    return new Promise((resolve) => {
      const key = `summary_${summaryType}_${year}_${month}_${day}`;
      chrome.storage.local.get([key], function(result) {
        resolve(result[key] || null);
      });
    });
  }

  // Synchronous version for immediate use
  function getSummaryValueSync(summaryType, year, month, day) {
    // For now, return null - values will be loaded asynchronously
    return null;
  }

  // Function to show daily aspects modal
  function showDailyAspectsModal(planetKey, year, month, day) {
    const modal = document.getElementById('daily-aspects-modal');
    const title = document.getElementById('daily-aspects-title');
    const planetName = document.getElementById('selected-planet-name');
    const selectedDate = document.getElementById('selected-date');
    const tbody = document.getElementById('daily-aspects-tbody');
    const noAspectsMessage = document.getElementById('no-aspects-message');
    const tableWrapper = document.querySelector('.daily-aspects-table-wrapper');

    if (!modal) return;

    // Set title and info
    const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                       'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const planetNames = {
      'sun': 'Soleil',
      'moon': 'Lune',
      'mercury': 'Mercure',
      'venus': 'Vénus',
      'mars': 'Mars',
      'jupiter': 'Jupiter',
      'saturn': 'Saturne',
      'uranus': 'Uranus',
      'neptune': 'Neptune'
    };

    title.textContent = `Détails des Aspects - ${day} ${monthNames[month]} ${year}`;
    planetName.textContent = planetNames[planetKey] || planetKey;
    selectedDate.textContent = `${day}/${month + 1}/${year}`;

    // Calculate aspects for this specific day and planet
    const transitDate = new Date(year, month, day, 12, 0, 0);
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );

    const birthPositions = calculatePlanetaryPositions(birthDate);
    const transitPositions = calculatePlanetaryPositions(transitDate);

    // Use current tab's filter settings for the modal
    const activeTab = document.querySelector('.monthly-tab-content.active');
    const tabId = activeTab ? activeTab.id : 'current-month';
    const selectedAspectTypes = getSelectedAspectTypes(tabId);
    const selectedOrb = getSelectedOrb(tabId);

    const dayAspects = isMonthlyTable360Mode ?
      calculate360DegreeAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes) :
      calculateProportionalAspects(transitPositions, birthPositions, selectedOrb, selectedAspectTypes);

    // Filter aspects for the selected natal planet
    const planetAspects = dayAspects.filter(aspect => aspect.natalPlanet === planetKey);

    // Clear previous content
    tbody.innerHTML = '';

    if (planetAspects.length === 0) {
      tableWrapper.style.display = 'none';
      noAspectsMessage.style.display = 'block';
    } else {
      tableWrapper.style.display = 'block';
      noAspectsMessage.style.display = 'none';

      // Populate table with aspects
      planetAspects.forEach(aspect => {
        const row = tbody.insertRow();

        // Transit planet
        const transitPlanetCell = row.insertCell();
        transitPlanetCell.textContent = planetNames[aspect.transitPlanet] || aspect.transitPlanet;
        transitPlanetCell.className = 'transit-planet';

        // Transit position
        const transitPosCell = row.insertCell();
        transitPosCell.textContent = formatPosition(aspect.transitPosition);
        transitPosCell.className = 'position-value';

        // Aspect type
        const aspectCell = row.insertCell();
        aspectCell.textContent = getAspectFrenchName(aspect.aspectType);
        aspectCell.className = `aspect-type aspect-${aspect.aspectType}`;

        // Orb
        const orbCell = row.insertCell();
        orbCell.textContent = `${Math.abs(aspect.orb).toFixed(2)}°`;
        orbCell.className = 'orb-value';

        // Natal planet
        const natalPlanetCell = row.insertCell();
        natalPlanetCell.textContent = planetNames[aspect.natalPlanet] || aspect.natalPlanet;
        natalPlanetCell.className = 'natal-planet';

        // Natal position
        const natalPosCell = row.insertCell();
        natalPosCell.textContent = formatPosition(aspect.natalPosition);
        natalPosCell.className = 'position-value';
      });
    }

    // Show modal
    modal.style.display = 'block';
  }

  // Function to format position (degrees to sign and degrees)
  function formatPosition(degrees) {
    const signs = ['Bélier', 'Taureau', 'Gémeaux', 'Cancer', 'Lion', 'Vierge',
                   'Balance', 'Scorpion', 'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'];

    // Normalize degrees to 0-360
    const normalizedDegrees = ((degrees % 360) + 360) % 360;

    // Calculate sign and position within sign
    const signIndex = Math.floor(normalizedDegrees / 30);
    const degreesInSign = normalizedDegrees % 30;

    return `${degreesInSign.toFixed(1)}° ${signs[signIndex]}`;
  }

  // Function to get French aspect name
  function getAspectFrenchName(aspectType) {
    const aspectNames = {
      'conjunction': 'Conjonction',
      'victoire-martial': 'Victoire Martial',
      'trigone-2': 'Trigone 2',
      'sextile': 'Sextile',
      'square': 'Carré',
      'trine': 'Trigone',
      'trigone-3': 'Trigone 3',
      'opposition': 'Opposition'
    };
    return aspectNames[aspectType] || aspectType;
  }

  // Initialize daily aspects modal
  function initDailyAspectsModal() {
    const modal = document.getElementById('daily-aspects-modal');
    const closeBtn = document.getElementById('close-daily-aspects');

    if (!modal || !closeBtn) return;

    // Close modal when X is clicked
    closeBtn.addEventListener('click', function() {
      modal.style.display = 'none';
    });

    // Close modal when clicking outside
    modal.addEventListener('click', function(event) {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    });
  }

  // Initialize mini calendar functionality
  function initMiniCalendar() {
    const prevBtn = document.getElementById('mini-calendar-prev');
    const nextBtn = document.getElementById('mini-calendar-next');
    const title = document.getElementById('mini-calendar-title');
    const grid = document.getElementById('mini-calendar-grid');

    if (!prevBtn || !nextBtn || !title || !grid) return;

    let currentCalendarDate = new Date();

    // Initialize calendar
    generateMiniCalendar(currentCalendarDate);

    // Previous month
    prevBtn.addEventListener('click', function() {
      currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
      generateMiniCalendar(currentCalendarDate);
    });

    // Next month
    nextBtn.addEventListener('click', function() {
      currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
      generateMiniCalendar(currentCalendarDate);
    });
  }

  // Generate mini calendar for a specific month
  function generateMiniCalendar(date) {
    const title = document.getElementById('mini-calendar-title');
    const grid = document.getElementById('mini-calendar-grid');

    if (!title || !grid) return;

    const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                       'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];

    const dayNames = ['D', 'L', 'M', 'M', 'J', 'V', 'S'];

    // Update title
    title.textContent = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;

    // Clear grid
    grid.innerHTML = '';

    // Add day headers
    dayNames.forEach(dayName => {
      const dayHeader = document.createElement('div');
      dayHeader.className = 'mini-calendar-day-header';
      dayHeader.textContent = dayName;
      grid.appendChild(dayHeader);
    });

    // Get first day of month and number of days
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    // Get current date for highlighting
    const today = new Date();
    const isCurrentMonth = date.getFullYear() === today.getFullYear() &&
                          date.getMonth() === today.getMonth();

    // Get selected transit date for highlighting
    const transitInput = document.getElementById('transit-date');
    let transitDate = new Date();
    let isSelectedMonth = false;

    if (transitInput && transitInput.value) {
      transitDate = new Date(transitInput.value);
      isSelectedMonth = date.getFullYear() === transitDate.getFullYear() &&
                       date.getMonth() === transitDate.getMonth();
    }

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      const emptyDay = document.createElement('div');
      emptyDay.className = 'mini-calendar-day other-month';
      grid.appendChild(emptyDay);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dayElement = document.createElement('div');
      dayElement.className = 'mini-calendar-day';
      dayElement.textContent = day;
      dayElement.setAttribute('data-day', day);
      dayElement.setAttribute('data-month', date.getMonth());
      dayElement.setAttribute('data-year', date.getFullYear());

      // Highlight current day
      if (isCurrentMonth && day === today.getDate()) {
        dayElement.classList.add('current-day');
      }

      // Highlight selected transit date
      if (isSelectedMonth && day === transitDate.getDate()) {
        dayElement.classList.add('selected-day');
      }

      // Add click handler for navigation
      dayElement.addEventListener('click', function() {
        const clickedDay = parseInt(this.getAttribute('data-day'));
        const clickedMonth = parseInt(this.getAttribute('data-month'));
        const clickedYear = parseInt(this.getAttribute('data-year'));

        navigateToDateFromCalendar(clickedYear, clickedMonth, clickedDay);
      });

      // Add right-click handler for color customization
      dayElement.addEventListener('contextmenu', function(e) {
        e.preventDefault(); // Prevent default context menu

        if (window.showDayColorContextMenu) {
          window.showDayColorContextMenu(this, e.clientX, e.clientY);
        }
      });

      // Add hover handlers for comment tooltip
      dayElement.addEventListener('mouseenter', function(e) {
        const comment = this.getAttribute('data-comment');
        if (comment && comment.length > 0) {
          showCommentTooltip(this, comment, e);
        }
      });

      dayElement.addEventListener('mouseleave', function() {
        hideCommentTooltip();
      });

      grid.appendChild(dayElement);
    }

    // Apply saved colors after generating the calendar
    setTimeout(() => {
      if (window.applySavedDayColors) {
        window.applySavedDayColors();
      }
    }, 100);
  }

  // Navigate to date from mini calendar
  function navigateToDateFromCalendar(year, month, day) {
    // Find the transit date input field by ID
    const transitInput = document.getElementById('transit-date');
    const transitHour = document.getElementById('transit-hour');
    const transitMin = document.getElementById('transit-minute');
    const transitSec = document.getElementById('transit-second');

    if (transitInput) {
      // For date input type, use ISO format
      const isoDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      transitInput.value = isoDate;

      // Trigger change event
      transitInput.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Update transit time fields
    if (transitHour) {
      transitHour.value = '12'; // Set to noon
      transitHour.dispatchEvent(new Event('change', { bubbles: true }));
    }
    if (transitMin) {
      transitMin.value = '0';
      transitMin.dispatchEvent(new Event('change', { bubbles: true }));
    }
    if (transitSec) {
      transitSec.value = '0';
      transitSec.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Switch to manual mode if in auto mode
    const toggleBtn = document.getElementById('toggle-transit-mode');
    if (toggleBtn && toggleBtn.textContent.includes('Mode Manuel')) {
      // Currently in auto mode, switch to manual
      toggleBtn.click();
    }

    // Trigger calculate chart
    const calculateBtn = document.getElementById('calculate-btn');
    if (calculateBtn) {
      calculateBtn.click();
    }

    // Update day comments display
    if (window.updateDayCommentsDisplay) {
      window.updateDayCommentsDisplay().catch(console.error);
    }

    // Update mini calendar to show new selection
    generateMiniCalendar(new Date(year, month, 1));

    // Show success message in console
    console.log(`Navigation vers le ${day}/${month + 1}/${year}`);

    // Show visual feedback
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;
    messageDiv.textContent = `Navigation vers le ${day}/${month + 1}/${year}`;
    document.body.appendChild(messageDiv);

    // Remove message after 3 seconds
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.parentNode.removeChild(messageDiv);
      }
    }, 3000);
  }

  // Initialize day color customization functionality
  function initDayColorCustomization() {
    const contextMenu = document.getElementById('day-color-context-menu');
    const contextMenuDate = document.getElementById('context-menu-date');
    const commentInput = document.getElementById('day-comment');
    const commentCounter = document.getElementById('comment-char-count');
    const applyBtn = document.getElementById('apply-day-color');
    const resetBtn = document.getElementById('reset-day-color');
    const cancelBtn = document.getElementById('cancel-day-color');

    let currentDayElement = null;
    let selectedBackgroundColor = 'default';
    let selectedTextColor = 'default';

    // Load saved colors from chrome.storage.sync
    let savedColors = {};
    loadCalendarColorsFromStorage();

    // Function to load calendar colors from chrome.storage.sync
    async function loadCalendarColorsFromStorage() {
      try {
        savedColors = await StorageMigration.loadCalendarColors();
        // Apply colors after loading
        setTimeout(() => {
          if (window.applySavedDayColors) {
            window.applySavedDayColors();
          }
        }, 100);
      } catch (error) {
        console.error('Erreur lors du chargement des couleurs du calendrier:', error);
        // Fallback to localStorage
        const localColors = localStorage.getItem('miniCalendarColors');
        savedColors = localColors ? JSON.parse(localColors) : {};
      }
    }

    // Function to save calendar colors to chrome.storage.sync
    async function saveCalendarColorsToStorage(colors) {
      try {
        await StorageMigration.saveCalendarColors(colors);
      } catch (error) {
        console.error('Erreur lors de la sauvegarde des couleurs du calendrier:', error);
        // Fallback to localStorage
        localStorage.setItem('miniCalendarColors', JSON.stringify(colors));
      }
    }

    // Comment input character counter
    if (commentInput && commentCounter) {
      commentInput.addEventListener('input', function() {
        const length = this.value.length;
        commentCounter.textContent = length;

        // Update counter color based on length
        const counterElement = commentCounter.parentElement;
        counterElement.classList.remove('warning', 'danger');

        if (length > 160) {
          counterElement.classList.add('danger');
        } else if (length > 120) {
          counterElement.classList.add('warning');
        }
      });
    }

    // Hide context menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!contextMenu.contains(e.target)) {
        hideContextMenu();
      }
    });

    // Prevent context menu from closing when clicking inside it
    contextMenu.addEventListener('click', function(e) {
      e.stopPropagation();
    });

    // Color option selection
    document.querySelectorAll('.color-option').forEach(option => {
      option.addEventListener('click', function() {
        // Remove selected class from all color options
        document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
        // Add selected class to clicked option
        this.classList.add('selected');
        selectedBackgroundColor = this.getAttribute('data-color');
      });
    });

    // Text color option selection
    document.querySelectorAll('.text-color-option').forEach(option => {
      option.addEventListener('click', function() {
        // Remove selected class from all text color options
        document.querySelectorAll('.text-color-option').forEach(opt => opt.classList.remove('selected'));
        // Add selected class to clicked option
        this.classList.add('selected');
        selectedTextColor = this.getAttribute('data-text-color');
      });
    });

    // Apply color button
    applyBtn.addEventListener('click', function() {
      if (currentDayElement) {
        const comment = commentInput ? commentInput.value.trim() : '';
        applyColorToDay(currentDayElement, selectedBackgroundColor, selectedTextColor, comment);
        hideContextMenu();
      }
    });

    // Reset color button
    resetBtn.addEventListener('click', function() {
      if (currentDayElement) {
        resetDayColor(currentDayElement);
        hideContextMenu();
      }
    });

    // Cancel button
    cancelBtn.addEventListener('click', function() {
      hideContextMenu();
    });

    // Function to show context menu
    function showContextMenu(dayElement, x, y) {
      currentDayElement = dayElement;
      const day = dayElement.getAttribute('data-day');
      const month = dayElement.getAttribute('data-month');
      const year = dayElement.getAttribute('data-year');

      if (day && month !== null && year) {
        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                           'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        contextMenuDate.textContent = `${day} ${monthNames[parseInt(month)]} ${year}`;

        // Get current colors and comment for this day
        const dayKey = `${year}-${month}-${day}`;
        const currentData = savedColors[dayKey] || { background: 'default', text: 'default', comment: '' };

        // Reset selections
        document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
        document.querySelectorAll('.text-color-option').forEach(opt => opt.classList.remove('selected'));

        // Select current colors
        const bgOption = document.querySelector(`[data-color="${currentData.background}"]`);
        const textOption = document.querySelector(`[data-text-color="${currentData.text}"]`);

        if (bgOption) {
          bgOption.classList.add('selected');
          selectedBackgroundColor = currentData.background;
        }
        if (textOption) {
          textOption.classList.add('selected');
          selectedTextColor = currentData.text;
        }

        // Load existing comment
        if (commentInput) {
          commentInput.value = currentData.comment || '';
          // Trigger input event to update counter
          commentInput.dispatchEvent(new Event('input'));
        }

        // Position and show context menu
        contextMenu.style.left = x + 'px';
        contextMenu.style.top = y + 'px';
        contextMenu.style.display = 'block';

        // Adjust position if menu goes off screen
        const rect = contextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
          contextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
          contextMenu.style.top = (y - rect.height) + 'px';
        }
      }
    }

    // Function to hide context menu
    function hideContextMenu() {
      contextMenu.style.display = 'none';
      currentDayElement = null;
    }

    // Function to apply color to day
    function applyColorToDay(dayElement, backgroundColor, textColor, comment) {
      const day = dayElement.getAttribute('data-day');
      const month = dayElement.getAttribute('data-month');
      const year = dayElement.getAttribute('data-year');
      const dayKey = `${year}-${month}-${day}`;

      // Apply styles
      if (backgroundColor === 'default') {
        dayElement.style.backgroundColor = '';
      } else {
        dayElement.style.backgroundColor = backgroundColor;
      }

      if (textColor === 'default') {
        dayElement.style.color = '';
      } else {
        dayElement.style.color = textColor;
      }

      // Handle comment indicator
      if (comment && comment.length > 0) {
        dayElement.classList.add('has-comment');
        dayElement.setAttribute('data-comment', comment);
      } else {
        dayElement.classList.remove('has-comment');
        dayElement.removeAttribute('data-comment');
      }

      // Add custom color class and animation
      dayElement.classList.add('custom-color', 'color-applied');
      setTimeout(() => dayElement.classList.remove('color-applied'), 500);

      // Save to chrome.storage.sync
      savedColors[dayKey] = {
        background: backgroundColor,
        text: textColor,
        comment: comment || ''
      };
      saveCalendarColorsToStorage(savedColors);
    }

    // Function to reset day color
    function resetDayColor(dayElement) {
      const day = dayElement.getAttribute('data-day');
      const month = dayElement.getAttribute('data-month');
      const year = dayElement.getAttribute('data-year');
      const dayKey = `${year}-${month}-${day}`;

      // Reset styles
      dayElement.style.backgroundColor = '';
      dayElement.style.color = '';
      dayElement.classList.remove('custom-color', 'has-comment');
      dayElement.removeAttribute('data-comment');

      // Remove from chrome.storage.sync
      delete savedColors[dayKey];
      saveCalendarColorsToStorage(savedColors);
    }

    // Function to apply saved colors to calendar
    function applySavedColors() {
      document.querySelectorAll('.mini-calendar-day[data-day]').forEach(dayElement => {
        const day = dayElement.getAttribute('data-day');
        const month = dayElement.getAttribute('data-month');
        const year = dayElement.getAttribute('data-year');
        const dayKey = `${year}-${month}-${day}`;

        if (savedColors[dayKey]) {
          const data = savedColors[dayKey];
          if (data.background !== 'default') {
            dayElement.style.backgroundColor = data.background;
          }
          if (data.text !== 'default') {
            dayElement.style.color = data.text;
          }
          if (data.comment && data.comment.length > 0) {
            dayElement.classList.add('has-comment');
            dayElement.setAttribute('data-comment', data.comment);
          }
          dayElement.classList.add('custom-color');
        }
      });
    }

    // Make functions available globally
    window.showDayColorContextMenu = showContextMenu;
    window.applySavedDayColors = applySavedColors;
  }

  // Functions for comment tooltips
  let commentTooltip = null;

  function showCommentTooltip(dayElement, comment, event) {
    // Remove existing tooltip
    hideCommentTooltip();

    // Create tooltip element
    commentTooltip = document.createElement('div');
    commentTooltip.className = 'day-comment-tooltip';
    commentTooltip.textContent = comment;
    document.body.appendChild(commentTooltip);

    // Position tooltip
    const rect = dayElement.getBoundingClientRect();
    const tooltipRect = commentTooltip.getBoundingClientRect();

    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    let top = rect.top - tooltipRect.height - 10;

    // Adjust if tooltip goes off screen
    if (left < 10) left = 10;
    if (left + tooltipRect.width > window.innerWidth - 10) {
      left = window.innerWidth - tooltipRect.width - 10;
    }
    if (top < 10) {
      top = rect.bottom + 10;
    }

    commentTooltip.style.left = left + 'px';
    commentTooltip.style.top = top + 'px';

    // Show tooltip with animation
    setTimeout(() => {
      if (commentTooltip) {
        commentTooltip.classList.add('show');
      }
    }, 100);
  }

  function hideCommentTooltip() {
    if (commentTooltip) {
      commentTooltip.classList.remove('show');
      setTimeout(() => {
        if (commentTooltip && commentTooltip.parentNode) {
          commentTooltip.parentNode.removeChild(commentTooltip);
        }
        commentTooltip = null;
      }, 200);
    }
  }

  // Function to apply color coding to summary cells
  function applySummaryColorCoding(tabId) {
    const tbody = document.getElementById(`${tabId}-body`);
    if (!tbody) return;

    // Only apply to current month for now
    if (tabId !== 'current-month') return;

    const summaryTypes = ['general-positivity', 'event-importance', 'surprise-level'];

    summaryTypes.forEach(summaryType => {
      const cells = tbody.querySelectorAll(`[data-summary-type="${summaryType}"]`);
      const values = [];
      const cellValueMap = new Map();

      // Collect all numeric values for this summary type
      cells.forEach(cell => {
        const value = parseFloat(cell.textContent.trim());
        if (!isNaN(value)) {
          values.push(value);
          cellValueMap.set(cell, value);
        } else {
          // Reset style for non-numeric cells
          cell.style.backgroundColor = '';
          cell.style.color = '';
          cell.style.fontWeight = '';
          cell.style.border = '';
          cell.style.boxShadow = '';
        }
      });

      if (values.length === 0) return;

      // Find min and max values
      const minValue = Math.min(...values);
      const maxValue = Math.max(...values);

      // Apply colors to each cell
      cellValueMap.forEach((value, cell) => {
        // Calculate color based on value
        const color = getValueColor(value, minValue, maxValue);

        // Apply background color
        cell.style.backgroundColor = color.background;
        cell.style.color = color.text;

        // Highlight extreme values
        if (value === minValue || value === maxValue) {
          cell.style.fontWeight = 'bold';
          cell.style.border = '2px solid #333';
          cell.style.boxShadow = '0 0 8px rgba(0,0,0,0.3)';

          // Add extreme value indicator
          if (value === minValue) {
            cell.title = `Valeur minimale: ${value}`;
          } else {
            cell.title = `Valeur maximale: ${value}`;
          }
        } else {
          cell.style.fontWeight = 'normal';
          cell.style.border = '';
          cell.style.boxShadow = '';
          cell.title = `Valeur: ${value}`;
        }
      });
    });
  }

  // Function to get color based on value
  function getValueColor(value, minValue, maxValue) {
    // Determine if we're dealing with mostly positive, negative, or mixed values
    const hasPositive = maxValue > 0;
    const hasNegative = minValue < 0;

    let normalizedValue;

    if (hasPositive && hasNegative) {
      // Mixed values: normalize around 0
      const maxAbs = Math.max(Math.abs(minValue), Math.abs(maxValue));
      normalizedValue = maxAbs > 0 ? value / maxAbs : 0;
    } else if (hasPositive) {
      // Only positive values: normalize 0 to 1
      normalizedValue = maxValue > 0 ? value / maxValue : 0;
    } else {
      // Only negative values: normalize -1 to 0
      normalizedValue = minValue < 0 ? value / Math.abs(minValue) : 0;
    }

    // Clamp between -1 and 1
    normalizedValue = Math.max(-1, Math.min(1, normalizedValue));

    let backgroundColor, textColor;

    if (normalizedValue < 0) {
      // Negative values: gradient to red
      const intensity = Math.abs(normalizedValue);
      const red = 255;
      const green = Math.round(255 * (1 - intensity * 0.9));
      const blue = Math.round(255 * (1 - intensity * 0.9));

      backgroundColor = `rgb(${red}, ${green}, ${blue})`;
      textColor = intensity > 0.5 ? 'white' : '#333';
    } else if (normalizedValue > 0) {
      // Positive values: gradient to green
      const intensity = normalizedValue;
      const red = Math.round(255 * (1 - intensity * 0.9));
      const green = 255;
      const blue = Math.round(255 * (1 - intensity * 0.9));

      backgroundColor = `rgb(${red}, ${green}, ${blue})`;
      textColor = intensity > 0.5 ? 'white' : '#333';
    } else {
      // Zero or neutral: light gray
      backgroundColor = '#f8f9fa';
      textColor = '#333';
    }

    return {
      background: backgroundColor,
      text: textColor
    };
  }

  // Function to add sector changes section for slow planets
  function addSectorChangesSection(tbody, targetDate) {
    const year = targetDate.getFullYear();
    const month = targetDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Add separator row
    const separatorRow = tbody.insertRow();
    const separatorCell = separatorRow.insertCell();
    separatorCell.colSpan = daysInMonth + 1;
    separatorCell.className = 'sector-changes-separator';
    const changeType = isMonthlyTable360Mode ? 'CHANGEMENT DE SIGNES' : 'CHANGEMENT DE SECTEURS';
    const astresText = isMonthlyTable360Mode ? 'tous astres' : 'astre lent';
    const modeText = isMonthlyTable360Mode ? 'mode 360°' : 'mode 30°';
    separatorCell.innerHTML = `<strong>${changeType} (${astresText} ${modeText})</strong>`;

    // Define planets based on mode
    const planets = isMonthlyTable360Mode ? [
      // Mode 360° : Tous les astres (rapides et lents) pour les changements de signes
      { key: 'sun', name: 'Soleil' },
      { key: 'moon', name: 'Lune' },
      { key: 'mercury', name: 'Mercure' },
      { key: 'venus', name: 'Vénus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ] : [
      // Mode 30° : Seulement les astres lents pour les changements de secteurs
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // No need for birth positions for sector changes

    planets.forEach(planet => {
      const row = tbody.insertRow();

      // Planet name cell
      const planetCell = row.insertCell();
      planetCell.textContent = planet.name;
      planetCell.className = 'planet-name-cell';

      // Calculate sector changes for each day
      let previousSector = null;

      for (let day = 1; day <= daysInMonth; day++) {
        const dayCell = row.insertCell();
        dayCell.className = 'day-cell sector-change-cell';
        dayCell.setAttribute('data-planet', planet.key);
        dayCell.setAttribute('data-day', day);
        dayCell.setAttribute('data-month', month);
        dayCell.setAttribute('data-year', year);

        // Calculate transit position for this day
        const transitDate = new Date(year, month, day, 12, 0, 0);
        const transitPositions = calculatePlanetaryPositions(transitDate);

        if (transitPositions[planet.key]) {
          const planetData = transitPositions[planet.key];

          if (isMonthlyTable360Mode) {
            // Mode 360° - détecter les changements de signes
            const signs = ['Bélier', 'Taureau', 'Gémeaux', 'Cancer', 'Lion', 'Vierge',
                          'Balance', 'Scorpion', 'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'];
            const signsEn = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                            'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];

            const currentSignIndex = signsEn.indexOf(planetData.sign);
            const currentSignName = signs[currentSignIndex];

            if (previousSector !== null && currentSignIndex !== previousSector) {
              // Changement de signe détecté
              const previousSignName = signs[previousSector];
              const degree = parseFloat(planetData.degree).toFixed(1);
              dayCell.textContent = `${previousSignName} → ${currentSignName} (${degree}°)`;
              dayCell.classList.add('sector-change-detected');
              dayCell.title = `${planet.name} entre en ${currentSignName} le ${day}/${month + 1}/${year}`;
            } else {
              dayCell.textContent = '-';
            }

            previousSector = currentSignIndex;
          } else {
            // Mode 30° - détecter les changements de secteurs (2.5° chacun)
            const transitPos = planetData.degree;
            const currentSector = Math.floor(transitPos / 2.5) + 1; // 12 sectors of 2.5° each (0-30°)

            if (previousSector !== null && currentSector !== previousSector) {
              // Changement de secteur détecté
              dayCell.textContent = `Secteur ${previousSector} → Secteur ${currentSector}`;
              dayCell.classList.add('sector-change-detected');
              dayCell.title = `${planet.name} change de secteur le ${day}/${month + 1}/${year}`;
            } else {
              dayCell.textContent = '-';
            }

            previousSector = currentSector;
          }
        } else {
          dayCell.textContent = '-';
        }
      }
    });
  }

  // Fonction pour mettre à jour l'affichage des commentaires du jour
  async function updateDayCommentsDisplay() {
    const commentDateElement = document.getElementById('comment-date');
    const commentTextElement = document.getElementById('comment-text');
    const commentIndicatorElement = document.getElementById('comment-indicator');

    if (!commentDateElement || !commentTextElement || !commentIndicatorElement) {
      return;
    }

    // Obtenir la date de transit actuelle
    const transitDateInput = document.getElementById('transit-date');
    if (!transitDateInput || !transitDateInput.value) {
      return;
    }

    const transitDate = new Date(transitDateInput.value);
    const dateKey = formatDateKey(transitDate);

    // Formater la date pour l'affichage
    const displayDate = formatDateForDisplay(transitDate);
    commentDateElement.textContent = displayDate;

    // Récupérer les commentaires sauvegardés depuis chrome.storage.sync (même système que le calendrier)
    try {
      const savedColors = await StorageMigration.loadCalendarColors();
      const comment = savedColors[dateKey];
      updateCommentsDisplay(comment, commentDateElement, commentTextElement, commentIndicatorElement, displayDate);
    } catch (error) {
      console.error('Erreur lors du chargement des commentaires:', error);
      // Fallback to localStorage
      const savedColors = JSON.parse(localStorage.getItem('miniCalendarColors') || '{}');
      const comment = savedColors[dateKey];
      updateCommentsDisplay(comment, commentDateElement, commentTextElement, commentIndicatorElement, displayDate);
    }
  }

  // Helper function to update comments display
  function updateCommentsDisplay(comment, commentDateElement, commentTextElement, commentIndicatorElement, displayDate) {
    commentDateElement.textContent = displayDate;

    if (comment && comment.comment && comment.comment.trim()) {
      // Il y a un commentaire pour ce jour
      commentTextElement.textContent = comment.comment;
      commentTextElement.classList.remove('no-comment');
      commentIndicatorElement.textContent = '💬';

      // Appliquer les couleurs personnalisées si elles existent
      const commentsDisplay = document.getElementById('day-comments-display');
      if (comment.background && comment.background !== 'default') {
        commentsDisplay.style.background = comment.background;
      } else {
        commentsDisplay.style.background = 'white';
      }

      if (comment.text && comment.text !== 'default') {
        commentTextElement.style.color = comment.text;
        commentDateElement.style.color = comment.text;
      } else {
        commentTextElement.style.color = 'black';
        commentDateElement.style.color = 'black';
      }
    } else {
      // Aucun commentaire pour ce jour
      commentTextElement.textContent = 'Aucun commentaire pour ce jour';
      commentTextElement.classList.add('no-comment');
      commentIndicatorElement.textContent = '📝';

      // Remettre les couleurs par défaut
      const commentsDisplay = document.getElementById('day-comments-display');
      commentsDisplay.style.background = 'white';
      commentTextElement.style.color = 'black';
      commentDateElement.style.color = 'black';
    }
  }

  // Fonction pour formater la clé de date (YYYY-M-D)
  function formatDateKey(date) {
    return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
  }

  // Fonction pour formater la date pour l'affichage (DD Mois YYYY)
  function formatDateForDisplay(date) {
    const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                       'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    return `${date.getDate()} ${monthNames[date.getMonth()]} ${date.getFullYear()}`;
  }

  // Function to initialize export/import functionality
  function initExportImportFunctionality() {
    const exportBtn = document.getElementById('export-data-btn');
    const importBtn = document.getElementById('import-data-btn');

    if (exportBtn) {
      exportBtn.addEventListener('click', exportAllData);
    }

    if (importBtn) {
      importBtn.addEventListener('click', importAllData);
    }
  }

  // Function to export all data
  async function exportAllData() {
    try {
      const allData = await StorageMigration.exportAllData();

      // Create a downloadable file
      const dataStr = JSON.stringify(allData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      // Create download link
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `astro-data-backup-${new Date().toISOString().split('T')[0]}.json`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      showSaveMessage('Données exportées avec succès !');
    } catch (error) {
      console.error('Erreur lors de l\'export:', error);
      showSaveMessage('Erreur lors de l\'export des données', true);
    }
  }

  // Function to import all data
  function importAllData() {
    // Create file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json';

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const data = JSON.parse(text);

        // Validate data structure
        if (typeof data !== 'object' || data === null) {
          throw new Error('Format de fichier invalide');
        }

        // Import data
        await StorageMigration.importAllData(data);

        showSaveMessage('Données importées avec succès ! Rechargez la page pour voir les changements.');

        // Optionally reload the page after a delay
        setTimeout(() => {
          window.location.reload();
        }, 2000);

      } catch (error) {
        console.error('Erreur lors de l\'import:', error);
        showSaveMessage('Erreur lors de l\'import des données: ' + error.message, true);
      }
    };

    // Trigger file selection
    fileInput.click();
  }

  // Make tooltip functions available globally
  window.showCommentTooltip = showCommentTooltip;
  window.hideCommentTooltip = hideCommentTooltip;
  window.applySummaryColorCoding = applySummaryColorCoding;
  window.updateDayCommentsDisplay = updateDayCommentsDisplay;

});
