// Système de gestion de la carte du ciel en grand
class FullsizeChartSystem {
  constructor() {
    this.isOpen = false;
    this.currentBirthPositions = null;
    this.currentTransitPositions = null;
    this.initializeEventListeners();
  }

  // Initialiser les écouteurs d'événements
  initializeEventListeners() {
    // Bouton principal pour ouvrir la modal
    const fullsizeBtn = document.getElementById('fullsize-chart-btn');
    if (fullsizeBtn) {
      fullsizeBtn.addEventListener('click', () => this.openModal());
    }

    // Fermeture de la modal
    const closeBtn = document.getElementById('close-fullsize-chart');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.closeModal());
    }

    // Bouton d'actualisation
    const refreshBtn = document.getElementById('fullsize-refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refreshChart());
    }

    // Bouton de téléchargement
    const downloadBtn = document.getElementById('fullsize-download-btn');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => this.downloadChart());
    }

    // Fermeture de la modal en cliquant à l'extérieur
    const modal = document.getElementById('fullsize-chart-modal');
    if (modal) {
      modal.addEventListener('click', (event) => {
        if (event.target === modal) {
          this.closeModal();
        }
      });
    }

    // Gestion des touches clavier
    document.addEventListener('keydown', (event) => {
      if (this.isOpen) {
        if (event.key === 'Escape') {
          this.closeModal();
        } else if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
          event.preventDefault();
          this.refreshChart();
        } else if (event.ctrlKey && event.key === 's') {
          event.preventDefault();
          this.downloadChart();
        }
      }
    });
  }

  // Ouvrir la modal
  openModal() {
    // Récupérer les positions actuelles
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;

    if (!this.currentBirthPositions || !this.currentTransitPositions) {
      alert('Veuillez d\'abord calculer une carte du ciel avant de l\'afficher en grand.');
      return;
    }

    const modal = document.getElementById('fullsize-chart-modal');
    if (modal) {
      modal.style.display = 'block';
      this.isOpen = true;

      // Mettre à jour les informations
      this.updateChartInfo();

      // Dessiner la carte après un court délai pour s'assurer que la modal est visible
      setTimeout(() => {
        this.drawFullsizeChart();
      }, 100);
    }
  }

  // Fermer la modal
  closeModal() {
    const modal = document.getElementById('fullsize-chart-modal');
    if (modal) {
      modal.style.display = 'none';
      this.isOpen = false;
    }
  }

  // Mettre à jour les informations de la carte
  updateChartInfo() {
    // Mettre à jour les dates de naissance et de transit
    this.updateDateTimeInfo();

    // Mettre à jour les informations de mode
    this.updateModeInfo();
  }

  // Mettre à jour les informations de date et heure
  updateDateTimeInfo() {
    const birthInfo = document.getElementById('fullsize-birth-info');
    const transitInfo = document.getElementById('fullsize-transit-info');

    if (birthInfo) {
      const birthDate = document.getElementById('birth-date')?.value;
      const birthHour = document.getElementById('birth-hour')?.value || '12';
      const birthMinute = document.getElementById('birth-minute')?.value || '0';

      if (birthDate) {
        const formattedBirthDate = new Date(birthDate).toLocaleDateString('fr-FR');
        birthInfo.textContent = `Naissance: ${formattedBirthDate} ${birthHour}:${birthMinute.padStart(2, '0')}`;
      } else {
        birthInfo.textContent = 'Naissance: 17/12/1991 07:27';
      }
    }

    if (transitInfo) {
      const transitDate = document.getElementById('transit-date')?.value;
      const transitHour = document.getElementById('transit-hour')?.value || '12';
      const transitMinute = document.getElementById('transit-minute')?.value || '0';

      if (transitDate) {
        const formattedTransitDate = new Date(transitDate).toLocaleDateString('fr-FR');
        transitInfo.textContent = `Transit: ${formattedTransitDate} ${transitHour}:${transitMinute.padStart(2, '0')}`;
      } else {
        const now = new Date();
        const formattedNow = now.toLocaleDateString('fr-FR');
        const currentHour = now.getHours().toString().padStart(2, '0');
        const currentMinute = now.getMinutes().toString().padStart(2, '0');
        transitInfo.textContent = `Transit: ${formattedNow} ${currentHour}:${currentMinute}`;
      }
    }
  }

  // Mettre à jour les informations de mode
  updateModeInfo() {
    const multiTransitInfo = document.getElementById('fullsize-multi-transit-info');
    const transitCount = document.getElementById('fullsize-transit-count');

    if (multiTransitInfo && transitCount) {
      const multiTransits = window.multiTransits || [];

      if (multiTransits.length > 0) {
        multiTransitInfo.style.display = 'inline';
        transitCount.textContent = multiTransits.length;
      } else {
        multiTransitInfo.style.display = 'none';
      }
    }
  }

  // Dessiner la carte en grand
  drawFullsizeChart() {
    const canvas = document.getElementById('fullsize-zodiac-canvas');
    if (!canvas || !this.currentBirthPositions || !this.currentTransitPositions) {
      console.error('Canvas ou positions manquantes pour dessiner la carte en grand');
      return;
    }

    // Configurer le canvas
    const ctx = canvas.getContext('2d');
    const width = canvas.width = 800;
    const height = canvas.height = 800;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 50;

    // Effacer le canvas
    ctx.clearRect(0, 0, width, height);

    // Utiliser la même fonction de dessin que le zodiaque principal mais avec des dimensions plus grandes
    this.drawFullsizeCircularZodiac(ctx, centerX, centerY, radius, this.currentBirthPositions, this.currentTransitPositions);
  }

  // Fonction de dessin adaptée pour la taille complète
  drawFullsizeCircularZodiac(ctx, centerX, centerY, radius, birthPositions, transitPositions) {
    // Calculer les rayons pour les différents cercles
    const outerRadius = radius;
    const innerRadius = radius * 0.7;

    // Dessiner les secteurs du zodiaque
    this.drawFullsizeZodiacSectors(ctx, centerX, centerY, innerRadius, outerRadius);

    // Dessiner les marquages de degrés
    this.drawFullsizeDegreeMarkings(ctx, centerX, centerY, outerRadius);

    // Dessiner les aspects entre planètes natales et de transit
    this.drawFullsizeAspects(ctx, centerX, centerY, innerRadius * 0.85, outerRadius * 0.92, birthPositions, transitPositions);

    // Dessiner les planètes natales (cercle intérieur)
    this.drawFullsizePlanets(ctx, centerX, centerY, innerRadius * 0.85, birthPositions, false);

    // Gérer les multi-transits
    const multiTransits = window.multiTransits || [];
    const multiTransitOptions = window.multiTransitOptions || { showCurrentTransit: true, autoResize: true, showLabels: false };

    if (multiTransits.length > 0) {
      // Dessiner les cercles et planètes multi-transit
      this.drawFullsizeMultiTransitCircles(ctx, centerX, centerY, outerRadius, multiTransits, multiTransitOptions);
    }

    // Dessiner le transit actuel si l'option est activée ou s'il n'y a pas de multi-transits
    if (multiTransitOptions.showCurrentTransit || multiTransits.length === 0) {
      // Dessiner les planètes de transit (cercle extérieur)
      this.drawFullsizePlanets(ctx, centerX, centerY, outerRadius * 0.92, transitPositions, true);
    }
  }

  // Dessiner les secteurs du zodiaque en grand
  drawFullsizeZodiacSectors(ctx, centerX, centerY, innerRadius, outerRadius) {
    // Utiliser la même logique que le zodiaque principal mais adapté pour la taille
    for (let sector = 1; sector <= 12; sector++) {
      const startAngle = ((sector - 1) * 2.5 * 12 - 90) * Math.PI / 180;
      const endAngle = (sector * 2.5 * 12 - 90) * Math.PI / 180;

      // Dessiner le secteur
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, outerRadius, startAngle, endAngle);
      ctx.closePath();

      // Couleur alternée pour les secteurs
      ctx.fillStyle = sector % 2 === 0 ? '#f8f9fa' : '#ffffff';
      ctx.fill();

      // Bordure du secteur
      ctx.strokeStyle = '#dee2e6';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Dessiner les lignes de séparation
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + outerRadius * Math.cos(startAngle),
        centerY + outerRadius * Math.sin(startAngle)
      );
      ctx.strokeStyle = '#adb5bd';
      ctx.lineWidth = 1;
      ctx.stroke();
    }

    // Dessiner le cercle intérieur
    ctx.beginPath();
    ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#6c757d';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Dessiner le cercle extérieur
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#6c757d';
    ctx.lineWidth = 2;
    ctx.stroke();
  }

  // Dessiner les marquages de degrés en grand
  drawFullsizeDegreeMarkings(ctx, centerX, centerY, radius) {
    ctx.save();
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = '#495057';

    // Dessiner les marquages tous les 2.5°
    for (let degree = 0; degree < 30; degree += 2.5) {
      const angle = ((degree * 12) - 90) * Math.PI / 180;
      const markRadius = radius + 25;

      const x = centerX + markRadius * Math.cos(angle);
      const y = centerY + markRadius * Math.sin(angle);

      ctx.fillText(`${degree}°`, x, y);
    }

    ctx.restore();
  }

  // Actualiser la carte
  refreshChart() {
    if (this.isOpen) {
      // Récupérer les nouvelles positions
      this.currentBirthPositions = window.birthPositions;
      this.currentTransitPositions = window.transitPositions;

      // Mettre à jour les informations
      this.updateChartInfo();

      // Redessiner
      this.drawFullsizeChart();
    }
  }

  // Dessiner les aspects en grand
  drawFullsizeAspects(ctx, centerX, centerY, natalRadius, transitRadius, birthPositions, transitPositions) {
    // Utiliser la fonction existante mais adaptée pour la taille
    if (window.drawProportionalAspects) {
      window.drawProportionalAspects(ctx, centerX, centerY, natalRadius, transitRadius, birthPositions, transitPositions);
    }
  }

  // Dessiner les planètes en grand
  drawFullsizePlanets(ctx, centerX, centerY, radius, positions, isTransit) {
    // Utiliser la fonction existante mais adaptée pour la taille
    if (window.drawProportionalPlanets) {
      window.drawProportionalPlanets(ctx, centerX, centerY, radius, positions, isTransit);
    }
  }

  // Dessiner les multi-transits en grand
  drawFullsizeMultiTransitCircles(ctx, centerX, centerY, baseRadius, multiTransits, options) {
    // Utiliser la fonction existante mais adaptée pour la taille
    if (window.drawMultiTransitCircles) {
      window.drawMultiTransitCircles(ctx, centerX, centerY, baseRadius, multiTransits, options);
    }
  }

  // Télécharger la carte
  downloadChart() {
    const canvas = document.getElementById('fullsize-zodiac-canvas');
    if (!canvas) return;

    try {
      // Créer un nom de fichier avec la date actuelle
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
      const filename = `carte-du-ciel-${dateStr}-${timeStr}.png`;

      // Créer un lien de téléchargement
      const link = document.createElement('a');
      link.download = filename;
      link.href = canvas.toDataURL('image/png');

      // Déclencher le téléchargement
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Carte du ciel téléchargée:', filename);
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      alert('Erreur lors du téléchargement de la carte du ciel.');
    }
  }
}

// Initialiser le système de carte en grand
let fullsizeChartSystem;

// Initialiser quand le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
  fullsizeChartSystem = new FullsizeChartSystem();
});
